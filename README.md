# Conditional DDPM and Improved CNN for Rolling Bearing Fault Diagnosis

This project implements the paper "Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN" using PyTorch.

## Overview

The project consists of:

1. **Conditional DDPM**: A denoising diffusion probabilistic model for generating synthetic bearing vibration data
2. **Improved CNN**: A CNN classifier with deformable convolution and adaptive pooling
3. **Data Augmentation**: Using synthetic data to improve classification performance on imbalanced datasets
4. **Evaluation**: GAN-train/GAN-test methods for assessing synthetic data quality

## Features

- Synthetic bearing vibration signal generation for 7 fault types
- Conditional DDPM for high-quality data augmentation
- Improved CNN architecture with deformable convolution
- Comprehensive evaluation using GAN-train/GAN-test methods
- Support for different imbalance ratios (1:2, 1:4, 1:10)
- Visualization and analysis tools

## Installation

1. Clone or download this repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### 1. Test the System

First, run the test script to ensure everything is working:

```bash
python test_modules.py
```

### 2. Run the Complete Pipeline

Run the full pipeline with default settings:

```bash
python main.py
```

For a quick test with reduced parameters:

```bash
python main.py --quick-test
```

### 3. Individual Components

You can also run individual components:

#### Train DDPM only:
```bash
python train_ddpm.py
```

#### Train classifier with augmentation:
```bash
python train_classifier.py
```

#### Run evaluation:
```bash
python evaluation.py
```

## Project Structure

```
├── config.py              # Configuration settings
├── data_generator.py       # Synthetic data generation
├── ddpm_model.py          # Conditional DDPM implementation
├── improved_cnn.py        # Improved CNN classifier
├── train_ddpm.py          # DDPM training script
├── train_classifier.py    # Classifier training script
├── evaluation.py          # Evaluation methods
├── main.py               # Main pipeline script
├── test_modules.py       # Test script
├── requirements.txt      # Dependencies
└── README.md            # This file
```

## Configuration

Key parameters can be modified in `config.py`:

- `sample_length`: Length of vibration signal samples (default: 1024)
- `sampling_rate`: Sampling frequency in Hz (default: 12000)
- `ddpm_timesteps`: Number of diffusion timesteps (default: 1000)
- `ddpm_epochs`: DDPM training epochs (default: 100)
- `cnn_epochs`: CNN training epochs (default: 50)
- `imbalance_ratios`: Ratios to test (default: [2, 4, 10])

## Fault Types

The system generates and classifies 7 types of bearing conditions:

1. **Normal**: Healthy bearing operation
2. **InnerRace**: Inner race fault
3. **OuterRace**: Outer race fault
4. **Ball**: Ball fault
5. **Cage**: Cage fault
6. **Combination1**: Inner + Outer race faults
7. **Combination2**: Ball + Cage faults

## Results

The pipeline generates:

- **Models**: Trained DDPM and CNN models in `./models/`
- **Figures**: Visualizations and plots in `./figures/`
- **Results**: Performance reports in `./results/`

Key outputs include:
- Signal examples for each fault type
- Generated synthetic samples
- Training loss curves
- Confusion matrices
- GAN-train/GAN-test evaluation results
- Comprehensive performance report

## Command Line Options

```bash
python main.py [OPTIONS]

Options:
  --skip-ddpm        Skip DDPM training
  --skip-classifier  Skip classifier experiments
  --skip-evaluation  Skip evaluation
  --quick-test       Run with reduced parameters for testing
```

## Methodology

### 1. Data Generation
- Simulates bearing vibration signals with different fault characteristics
- Uses bearing kinematics to generate realistic fault frequencies
- Adds noise and variations for diversity

### 2. Conditional DDPM
- Forward diffusion: Gradually adds Gaussian noise
- Reverse diffusion: U-Net denoises conditioned on fault labels
- Generates high-quality synthetic samples for data augmentation

### 3. Improved CNN
- Deformable convolution for adaptive feature extraction
- Adaptive pooling combining average and max pooling
- Designed for 1D vibration signal classification

### 4. Evaluation
- **GAN-train**: Train on synthetic, test on real data
- **GAN-test**: Train on real, test on synthetic data
- Diversity analysis of generated samples

## Performance

Expected improvements with data augmentation:
- 1:2 imbalance ratio: ~2-5% accuracy improvement
- 1:4 imbalance ratio: ~5-10% accuracy improvement
- 1:10 imbalance ratio: ~10-15% accuracy improvement

## Hardware Requirements

- **Minimum**: CPU with 8GB RAM
- **Recommended**: GPU with 4GB+ VRAM for faster training
- **Storage**: ~1GB for models and results

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch sizes in `config.py`
2. **Slow training**: Use GPU if available, or reduce epochs for testing
3. **Import errors**: Ensure all dependencies are installed

### Performance Tips

1. Use GPU for faster training
2. Adjust batch sizes based on available memory
3. Use `--quick-test` for initial testing
4. Monitor training with generated plots

## Citation

If you use this code, please cite the original paper:

```
Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising 
Diffusion Probabilistic Model and Improved CNN
IEEE Transactions on Instrumentation and Measurement, Vol. 74, 2025
```

## License

This implementation is for research and educational purposes.

## Contact

For questions or issues, please check the code comments or create an issue in the repository.
