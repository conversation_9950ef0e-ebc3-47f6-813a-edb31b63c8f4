"""
Configuration file for the Condition DDPM and Improved CNN project
"""

import torch

class Config:
    # Device configuration
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Data configuration
    sample_length = 1024  # Length of each vibration signal sample
    sampling_rate = 12000  # Sampling frequency in Hz
    window_step = 256  # Sliding window step size
    
    # Dataset configuration
    num_classes = 7  # Number of fault classes (6 faults + 1 normal)
    fault_types = ['Normal', 'InnerRace', 'OuterRace', 'Ball', 'Cage', 'Combination1', 'Combination2']
    
    # Imbalance ratios for experiments
    imbalance_ratios = [2, 4, 10]  # 1:2, 1:4, 1:10
    
    # DDPM configuration
    ddpm_timesteps = 1000
    ddpm_beta_start = 1e-4
    ddpm_beta_end = 0.02
    ddpm_channels = 64
    ddpm_learning_rate = 1e-4
    ddpm_epochs = 2000
    ddpm_batch_size = 32
    
    # CNN configuration
    cnn_channels = [32, 64, 128, 256]
    cnn_kernel_sizes = [7, 5, 3, 3]
    cnn_learning_rate = 1e-4
    cnn_epochs = 50
    cnn_batch_size = 64
    cnn_dropout = 0.2
    
    # Training configuration
    num_folds = 5  # For cross-validation
    train_test_split = 0.8
    
    # Paths
    data_dir = './data'
    models_dir = './models'
    results_dir = './results'
    figures_dir = './figures'
    
    # Visualization
    figure_size = (12, 8)
    dpi = 300
