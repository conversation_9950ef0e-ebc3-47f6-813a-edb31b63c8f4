"""
Data generator for simulating rolling bearing vibration signals
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from sklearn.preprocessing import MinMaxScaler
import torch
from torch.utils.data import Dataset, DataLoader
from config import Config

class BearingDataGenerator:
    """Generate synthetic bearing vibration data with different fault types"""

    def __init__(self, config=Config()):
        self.config = config
        self.fs = config.sampling_rate
        self.length = config.sample_length

        # Bearing parameters (typical for 6204 bearing)
        self.shaft_freq = 20  # Hz (1200 rpm)
        self.ball_pass_freq_outer = 5.4 * self.shaft_freq  # BPFO
        self.ball_pass_freq_inner = 7.6 * self.shaft_freq  # BPFI
        self.ball_spin_freq = 2.3 * self.shaft_freq  # BSF
        self.cage_freq = 0.4 * self.shaft_freq  # FTF

    def generate_normal_signal(self, duration=None):
        """Generate normal bearing vibration signal"""
        if duration is None:
            duration = self.length / self.fs

        t = np.linspace(0, duration, int(duration * self.fs))

        # Base vibration with multiple harmonics
        signal_base = 0.1 * np.sin(2 * np.pi * self.shaft_freq * t)
        signal_base += 0.05 * np.sin(2 * np.pi * 2 * self.shaft_freq * t)
        signal_base += 0.02 * np.sin(2 * np.pi * 3 * self.shaft_freq * t)

        # Add random noise
        noise = 0.1 * np.random.normal(0, 1, len(t))

        return signal_base + noise

    def generate_inner_race_fault(self, duration=None, fault_severity=1.0):
        """Generate inner race fault signal"""
        if duration is None:
            duration = self.length / self.fs

        t = np.linspace(0, duration, int(duration * self.fs))

        # Normal signal
        normal_signal = self.generate_normal_signal(duration)

        # Fault impulses at BPFI frequency
        impulse_times = np.arange(0, duration, 1/self.ball_pass_freq_inner)
        fault_signal = np.zeros_like(t)

        for imp_time in impulse_times:
            if imp_time < duration:
                # Create exponentially decaying impulse
                impulse_idx = int(imp_time * self.fs)
                if impulse_idx < len(t):
                    decay_length = int(0.001 * self.fs)  # 1ms decay
                    end_idx = min(impulse_idx + decay_length, len(t))
                    decay_t = np.arange(end_idx - impulse_idx) / self.fs
                    impulse = fault_severity * np.exp(-decay_t * 1000) * np.sin(2 * np.pi * 3000 * decay_t)
                    fault_signal[impulse_idx:end_idx] += impulse

        return normal_signal + fault_signal

    def generate_outer_race_fault(self, duration=None, fault_severity=1.0):
        """Generate outer race fault signal"""
        if duration is None:
            duration = self.length / self.fs

        t = np.linspace(0, duration, int(duration * self.fs))

        # Normal signal
        normal_signal = self.generate_normal_signal(duration)

        # Fault impulses at BPFO frequency
        impulse_times = np.arange(0, duration, 1/self.ball_pass_freq_outer)
        fault_signal = np.zeros_like(t)

        for imp_time in impulse_times:
            if imp_time < duration:
                impulse_idx = int(imp_time * self.fs)
                if impulse_idx < len(t):
                    decay_length = int(0.002 * self.fs)  # 2ms decay
                    end_idx = min(impulse_idx + decay_length, len(t))
                    decay_t = np.arange(end_idx - impulse_idx) / self.fs
                    impulse = fault_severity * np.exp(-decay_t * 800) * np.sin(2 * np.pi * 2500 * decay_t)
                    fault_signal[impulse_idx:end_idx] += impulse

        return normal_signal + fault_signal

    def generate_ball_fault(self, duration=None, fault_severity=1.0):
        """Generate ball fault signal"""
        if duration is None:
            duration = self.length / self.fs

        t = np.linspace(0, duration, int(duration * self.fs))

        # Normal signal
        normal_signal = self.generate_normal_signal(duration)

        # Fault impulses at BSF frequency (modulated by cage frequency)
        fault_signal = np.zeros_like(t)

        # Ball fault creates impulses twice per ball revolution
        impulse_freq = 2 * self.ball_spin_freq
        impulse_times = np.arange(0, duration, 1/impulse_freq)

        for imp_time in impulse_times:
            if imp_time < duration:
                impulse_idx = int(imp_time * self.fs)
                if impulse_idx < len(t):
                    decay_length = int(0.0015 * self.fs)  # 1.5ms decay
                    end_idx = min(impulse_idx + decay_length, len(t))
                    decay_t = np.arange(end_idx - impulse_idx) / self.fs
                    # Modulate amplitude with cage frequency
                    modulation = 1 + 0.5 * np.sin(2 * np.pi * self.cage_freq * imp_time)
                    impulse = fault_severity * modulation * np.exp(-decay_t * 1200) * np.sin(2 * np.pi * 3500 * decay_t)
                    fault_signal[impulse_idx:end_idx] += impulse

        return normal_signal + fault_signal

    def generate_cage_fault(self, duration=None, fault_severity=1.0):
        """Generate cage fault signal"""
        if duration is None:
            duration = self.length / self.fs

        t = np.linspace(0, duration, int(duration * self.fs))

        # Normal signal
        normal_signal = self.generate_normal_signal(duration)

        # Cage fault creates modulation at cage frequency
        modulation = 1 + fault_severity * 0.3 * np.sin(2 * np.pi * self.cage_freq * t)
        modulated_signal = normal_signal * modulation

        # Add some random impulses
        num_impulses = int(duration * self.cage_freq * 2)
        impulse_times = np.random.uniform(0, duration, num_impulses)

        for imp_time in impulse_times:
            impulse_idx = int(imp_time * self.fs)
            if impulse_idx < len(t):
                decay_length = int(0.003 * self.fs)  # 3ms decay
                end_idx = min(impulse_idx + decay_length, len(t))
                decay_t = np.arange(end_idx - impulse_idx) / self.fs
                impulse = fault_severity * 0.5 * np.exp(-decay_t * 500) * np.sin(2 * np.pi * 2000 * decay_t)
                modulated_signal[impulse_idx:end_idx] += impulse

        return modulated_signal

    def generate_combination_fault_1(self, duration=None, fault_severity=1.0):
        """Generate combination fault (inner + outer race)"""
        if duration is None:
            duration = self.length / self.fs

        inner_signal = self.generate_inner_race_fault(duration, fault_severity * 0.7)
        outer_signal = self.generate_outer_race_fault(duration, fault_severity * 0.5)
        normal_signal = self.generate_normal_signal(duration)

        return 0.3 * normal_signal + 0.4 * inner_signal + 0.3 * outer_signal

    def generate_combination_fault_2(self, duration=None, fault_severity=1.0):
        """Generate combination fault (ball + cage)"""
        if duration is None:
            duration = self.length / self.fs

        ball_signal = self.generate_ball_fault(duration, fault_severity * 0.6)
        cage_signal = self.generate_cage_fault(duration, fault_severity * 0.4)
        normal_signal = self.generate_normal_signal(duration)

        return 0.4 * normal_signal + 0.4 * ball_signal + 0.2 * cage_signal

    def generate_signal(self, fault_type, duration=None, fault_severity=1.0):
        """Generate signal based on fault type"""
        generators = {
            'Normal': self.generate_normal_signal,
            'InnerRace': self.generate_inner_race_fault,
            'OuterRace': self.generate_outer_race_fault,
            'Ball': self.generate_ball_fault,
            'Cage': self.generate_cage_fault,
            'Combination1': self.generate_combination_fault_1,
            'Combination2': self.generate_combination_fault_2
        }

        if fault_type not in generators:
            raise ValueError(f"Unknown fault type: {fault_type}")

        if fault_type == 'Normal':
            return generators[fault_type](duration)
        else:
            return generators[fault_type](duration, fault_severity)


class BearingDataset(Dataset):
    """PyTorch Dataset for bearing vibration data"""

    def __init__(self, signals, labels, transform=None):
        self.signals = signals
        self.labels = labels
        self.transform = transform

    def __len__(self):
        return len(self.signals)

    def __getitem__(self, idx):
        signal = self.signals[idx]
        label = self.labels[idx]

        if self.transform:
            signal = self.transform(signal)

        return torch.FloatTensor(signal), torch.LongTensor([label])


def create_balanced_dataset(config=Config(), samples_per_class=200):
    """Create a balanced dataset with equal samples per class"""
    generator = BearingDataGenerator(config)

    all_signals = []
    all_labels = []

    for class_idx, fault_type in enumerate(config.fault_types):
        print(f"Generating {samples_per_class} samples for {fault_type}...")

        for i in range(samples_per_class):
            # Vary fault severity for more diversity
            if fault_type != 'Normal':
                fault_severity = np.random.uniform(0.5, 2.0)
            else:
                fault_severity = 1.0

            signal = generator.generate_signal(fault_type, fault_severity=fault_severity)

            # Add some random noise
            noise_level = np.random.uniform(0.05, 0.15)
            signal += noise_level * np.random.normal(0, 1, len(signal))

            all_signals.append(signal)
            all_labels.append(class_idx)

    # Normalize signals
    scaler = MinMaxScaler(feature_range=(-1, 1))
    all_signals = np.array(all_signals)
    all_signals = scaler.fit_transform(all_signals)

    return all_signals, np.array(all_labels), scaler


def create_imbalanced_dataset(config=Config(), majority_samples=200, imbalance_ratio=4):
    """Create an imbalanced dataset"""
    generator = BearingDataGenerator(config)

    all_signals = []
    all_labels = []

    minority_samples = majority_samples // imbalance_ratio

    for class_idx, fault_type in enumerate(config.fault_types):
        if class_idx == 0:  # Normal class (majority)
            num_samples = majority_samples
        else:  # Fault classes (minority)
            num_samples = minority_samples

        print(f"Generating {num_samples} samples for {fault_type}...")

        for i in range(num_samples):
            if fault_type != 'Normal':
                fault_severity = np.random.uniform(0.5, 2.0)
            else:
                fault_severity = 1.0

            signal = generator.generate_signal(fault_type, fault_severity=fault_severity)

            # Add noise
            noise_level = np.random.uniform(0.05, 0.15)
            signal += noise_level * np.random.normal(0, 1, len(signal))

            all_signals.append(signal)
            all_labels.append(class_idx)

    # Normalize signals
    scaler = MinMaxScaler(feature_range=(-1, 1))
    all_signals = np.array(all_signals)
    all_signals = scaler.fit_transform(all_signals)

    return all_signals, np.array(all_labels), scaler


def sliding_window_samples(signal, window_size, step_size):
    """Create overlapping samples using sliding window"""
    samples = []
    for i in range(0, len(signal) - window_size + 1, step_size):
        samples.append(signal[i:i + window_size])
    return np.array(samples)


def visualize_signals(config=Config(), save_path=None):
    """Visualize example signals for each fault type"""
    generator = BearingDataGenerator(config)

    fig, axes = plt.subplots(len(config.fault_types), 2, figsize=(15, 3*len(config.fault_types)))

    for i, fault_type in enumerate(config.fault_types):
        signal = generator.generate_signal(fault_type)
        t = np.linspace(0, len(signal)/config.sampling_rate, len(signal))

        # Time domain
        axes[i, 0].plot(t, signal)
        axes[i, 0].set_title(f'{fault_type} - Time Domain')
        axes[i, 0].set_xlabel('Time (s)')
        axes[i, 0].set_ylabel('Amplitude')
        axes[i, 0].grid(True)

        # Frequency domain
        freqs = np.fft.fftfreq(len(signal), 1/config.sampling_rate)
        fft_signal = np.abs(np.fft.fft(signal))

        # Plot only positive frequencies up to Nyquist
        pos_freqs = freqs[:len(freqs)//2]
        pos_fft = fft_signal[:len(fft_signal)//2]

        axes[i, 1].plot(pos_freqs, pos_fft)
        axes[i, 1].set_title(f'{fault_type} - Frequency Domain')
        axes[i, 1].set_xlabel('Frequency (Hz)')
        axes[i, 1].set_ylabel('Magnitude')
        axes[i, 1].set_xlim(0, 1000)  # Focus on low frequencies
        axes[i, 1].grid(True)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=config.dpi, bbox_inches='tight')

    plt.show()


if __name__ == "__main__":
    # Test the data generator
    config = Config()

    # Visualize signals
    visualize_signals(config, 'signal_examples.png')

    # Create datasets
    print("Creating balanced dataset...")
    signals, labels, scaler = create_balanced_dataset(config, samples_per_class=100)
    print(f"Created dataset with shape: {signals.shape}")

    print("Creating imbalanced dataset...")
    imb_signals, imb_labels, imb_scaler = create_imbalanced_dataset(config, majority_samples=200, imbalance_ratio=4)
    print(f"Created imbalanced dataset with shape: {imb_signals.shape}")

    # Print class distribution
    unique, counts = np.unique(imb_labels, return_counts=True)
    print("Class distribution in imbalanced dataset:")
    for i, (class_idx, count) in enumerate(zip(unique, counts)):
        print(f"  {config.fault_types[class_idx]}: {count} samples")
