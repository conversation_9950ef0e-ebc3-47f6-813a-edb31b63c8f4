"""
Conditional Denoising Diffusion Probabilistic Model (DDPM) for bearing fault data augmentation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from tqdm import tqdm
from config import Config

def get_beta_schedule(timesteps, beta_start=1e-4, beta_end=0.02):
    """Linear beta schedule for DDPM"""
    return torch.linspace(beta_start, beta_end, timesteps)

def extract(a, t, x_shape):
    """Extract values from tensor a at indices t"""
    batch_size = t.shape[0]
    out = a.to(t.device).gather(-1, t)
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1)))

class SinusoidalPositionEmbeddings(nn.Module):
    """Sinusoidal position embeddings for time steps"""

    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings

class ResidualBlock(nn.Module):
    """Residual block for U-Net with time and label conditioning"""

    def __init__(self, in_channels, out_channels, time_emb_dim, num_classes, dropout=0.1):
        super().__init__()
        self.time_mlp = nn.Linear(time_emb_dim, out_channels)
        self.label_emb = nn.Embedding(num_classes, out_channels)

        self.block1 = nn.Sequential(
            nn.GroupNorm(8, in_channels),
            nn.SiLU(),
            nn.Conv1d(in_channels, out_channels, kernel_size=3, padding=1)
        )

        self.block2 = nn.Sequential(
            nn.GroupNorm(8, out_channels),
            nn.SiLU(),
            nn.Dropout(dropout),
            nn.Conv1d(out_channels, out_channels, kernel_size=3, padding=1)
        )

        if in_channels != out_channels:
            self.residual_conv = nn.Conv1d(in_channels, out_channels, kernel_size=1)
        else:
            self.residual_conv = nn.Identity()

    def forward(self, x, time_emb, label):
        h = self.block1(x)

        # Add time embedding
        time_emb = self.time_mlp(time_emb)
        h = h + time_emb[:, :, None]

        # Add label embedding
        label_emb = self.label_emb(label)
        h = h + label_emb[:, :, None]

        h = self.block2(h)

        return h + self.residual_conv(x)

class UNet1D(nn.Module):
    """1D U-Net for conditional DDPM"""

    def __init__(self, in_channels=1, out_channels=1, time_emb_dim=128, num_classes=7):
        super().__init__()

        # Time embedding
        self.time_mlp = nn.Sequential(
            SinusoidalPositionEmbeddings(time_emb_dim),
            nn.Linear(time_emb_dim, time_emb_dim),
            nn.SiLU(),
            nn.Linear(time_emb_dim, time_emb_dim)
        )

        # Encoder
        self.conv_in = nn.Conv1d(in_channels, 64, kernel_size=3, padding=1)

        self.down1 = ResidualBlock(64, 64, time_emb_dim, num_classes)
        self.down2 = ResidualBlock(64, 128, time_emb_dim, num_classes)
        self.down3 = ResidualBlock(128, 256, time_emb_dim, num_classes)
        self.down4 = ResidualBlock(256, 512, time_emb_dim, num_classes)

        # Middle
        self.middle = ResidualBlock(512, 512, time_emb_dim, num_classes)

        # Decoder
        self.up4 = ResidualBlock(512 + 512, 256, time_emb_dim, num_classes)
        self.up3 = ResidualBlock(256 + 256, 128, time_emb_dim, num_classes)
        self.up2 = ResidualBlock(128 + 128, 64, time_emb_dim, num_classes)
        self.up1 = ResidualBlock(64 + 64, 64, time_emb_dim, num_classes)

        self.conv_out = nn.Conv1d(64, out_channels, kernel_size=3, padding=1)

        # Pooling and upsampling
        self.pool = nn.MaxPool1d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='linear', align_corners=False)

    def forward(self, x, timestep, label):
        # Time embedding
        time_emb = self.time_mlp(timestep)

        # Encoder
        x1 = self.conv_in(x)
        x1 = self.down1(x1, time_emb, label)

        x2 = self.pool(x1)
        x2 = self.down2(x2, time_emb, label)

        x3 = self.pool(x2)
        x3 = self.down3(x3, time_emb, label)

        x4 = self.pool(x3)
        x4 = self.down4(x4, time_emb, label)

        # Middle
        x_mid = self.pool(x4)
        x_mid = self.middle(x_mid, time_emb, label)

        # Decoder
        x = self.upsample(x_mid)
        x = torch.cat([x, x4], dim=1)
        x = self.up4(x, time_emb, label)

        x = self.upsample(x)
        x = torch.cat([x, x3], dim=1)
        x = self.up3(x, time_emb, label)

        x = self.upsample(x)
        x = torch.cat([x, x2], dim=1)
        x = self.up2(x, time_emb, label)

        x = self.upsample(x)
        x = torch.cat([x, x1], dim=1)
        x = self.up1(x, time_emb, label)

        return self.conv_out(x)

class ConditionalDDPM(nn.Module):
    """Conditional Denoising Diffusion Probabilistic Model"""

    def __init__(self, config=Config()):
        super().__init__()
        self.config = config
        self.timesteps = config.ddpm_timesteps
        self.num_classes = config.num_classes

        # Beta schedule
        self.register_buffer('betas', get_beta_schedule(self.timesteps, config.ddpm_beta_start, config.ddpm_beta_end))

        # Pre-compute values
        alphas = 1.0 - self.betas
        self.register_buffer('alphas_cumprod', torch.cumprod(alphas, dim=0))
        self.register_buffer('alphas_cumprod_prev', F.pad(self.alphas_cumprod[:-1], (1, 0), value=1.0))
        self.register_buffer('sqrt_alphas_cumprod', torch.sqrt(self.alphas_cumprod))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', torch.sqrt(1.0 - self.alphas_cumprod))

        # Posterior variance
        self.register_buffer('posterior_variance',
                           self.betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod))

        # U-Net model
        self.model = UNet1D(in_channels=1, out_channels=1, num_classes=self.num_classes)

    def q_sample(self, x_start, t, noise=None):
        """Forward diffusion process"""
        if noise is None:
            noise = torch.randn_like(x_start)

        sqrt_alphas_cumprod_t = extract(self.sqrt_alphas_cumprod, t, x_start.shape)
        sqrt_one_minus_alphas_cumprod_t = extract(self.sqrt_one_minus_alphas_cumprod, t, x_start.shape)

        return sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise

    def p_losses(self, x_start, t, labels, noise=None):
        """Compute training loss"""
        if noise is None:
            noise = torch.randn_like(x_start)

        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise)
        predicted_noise = self.model(x_noisy, t, labels)

        loss = F.mse_loss(noise, predicted_noise)
        return loss

    def forward(self, x, labels):
        """Forward pass for training"""
        batch_size = x.shape[0]
        device = x.device

        # Sample random timesteps
        t = torch.randint(0, self.timesteps, (batch_size,), device=device).long()

        return self.p_losses(x, t, labels)

    @torch.no_grad()
    def p_sample(self, x, t, t_index, labels):
        """Single denoising step"""
        betas_t = extract(self.betas, t, x.shape)
        sqrt_one_minus_alphas_cumprod_t = extract(self.sqrt_one_minus_alphas_cumprod, t, x.shape)
        sqrt_recip_alphas_t = extract(1.0 / torch.sqrt(1.0 - self.betas), t, x.shape)

        # Predict noise
        model_mean = sqrt_recip_alphas_t * (
            x - betas_t * self.model(x, t, labels) / sqrt_one_minus_alphas_cumprod_t
        )

        if t_index == 0:
            return model_mean
        else:
            posterior_variance_t = extract(self.posterior_variance, t, x.shape)
            noise = torch.randn_like(x)
            return model_mean + torch.sqrt(posterior_variance_t) * noise

    @torch.no_grad()
    def p_sample_loop(self, shape, labels):
        """Full denoising loop"""
        device = next(self.model.parameters()).device

        b = shape[0]
        # Start from pure noise
        img = torch.randn(shape, device=device)

        for i in tqdm(reversed(range(0, self.timesteps)), desc='Sampling', total=self.timesteps):
            img = self.p_sample(img, torch.full((b,), i, device=device, dtype=torch.long), i, labels)

        return img

    @torch.no_grad()
    def sample(self, batch_size, labels, signal_length=1024):
        """Generate samples"""
        return self.p_sample_loop((batch_size, 1, signal_length), labels)
