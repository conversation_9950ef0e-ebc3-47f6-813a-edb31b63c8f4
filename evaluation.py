"""
Evaluation methods including GAN-train/GAN-test for synthetic data quality assessment
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import train_test_split
import seaborn as sns
from tqdm import tqdm
import os

from config import Config
from improved_cnn import ImprovedCNN, BasicCNN
from data_generator import create_balanced_dataset
from train_ddpm import generate_samples

class GANTrainTest:
    """Implementation of GAN-train and GAN-test evaluation methods"""

    def __init__(self, config=Config()):
        self.config = config
        self.device = config.device

    def gan_train_evaluation(self, synthetic_data, synthetic_labels, real_test_data, real_test_labels):
        """
        GAN-Train: Train classifier on synthetic data, test on real data
        High score indicates synthetic data is diverse and realistic
        """
        print("Running GAN-Train evaluation...")

        # Create datasets
        synthetic_dataset = TensorDataset(
            torch.FloatTensor(synthetic_data),
            torch.LongTensor(synthetic_labels)
        )
        real_test_dataset = TensorDataset(
            torch.FloatTensor(real_test_data),
            torch.LongTensor(real_test_labels)
        )

        # Create data loaders
        train_loader = DataLoader(synthetic_dataset, batch_size=self.config.cnn_batch_size, shuffle=True)
        test_loader = DataLoader(real_test_dataset, batch_size=self.config.cnn_batch_size, shuffle=False)

        # Initialize model
        model = ImprovedCNN(self.config).to(self.device)
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=self.config.cnn_learning_rate)

        # Train on synthetic data
        model.train()
        for epoch in range(20):  # Reduced epochs for evaluation
            for data, labels in train_loader:
                data, labels = data.to(self.device), labels.squeeze().to(self.device)

                optimizer.zero_grad()
                outputs = model(data)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()

        # Test on real data
        model.eval()
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for data, labels in test_loader:
                data, labels = data.to(self.device), labels.squeeze().to(self.device)
                outputs = model(data)
                _, predicted = torch.max(outputs, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        accuracy = accuracy_score(all_labels, all_predictions)
        return accuracy * 100  # Return as percentage

    def gan_test_evaluation(self, real_train_data, real_train_labels, synthetic_test_data, synthetic_test_labels):
        """
        GAN-Test: Train classifier on real data, test on synthetic data
        High score indicates synthetic data is realistic enough to be recognized by real-trained classifier
        """
        print("Running GAN-Test evaluation...")

        # Create datasets
        real_train_dataset = TensorDataset(
            torch.FloatTensor(real_train_data),
            torch.LongTensor(real_train_labels)
        )
        synthetic_test_dataset = TensorDataset(
            torch.FloatTensor(synthetic_test_data),
            torch.LongTensor(synthetic_test_labels)
        )

        # Create data loaders
        train_loader = DataLoader(real_train_dataset, batch_size=self.config.cnn_batch_size, shuffle=True)
        test_loader = DataLoader(synthetic_test_dataset, batch_size=self.config.cnn_batch_size, shuffle=False)

        # Initialize model
        model = ImprovedCNN(self.config).to(self.device)
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=self.config.cnn_learning_rate)

        # Train on real data
        model.train()
        for epoch in range(20):  # Reduced epochs for evaluation
            for data, labels in train_loader:
                data, labels = data.to(self.device), labels.squeeze().to(self.device)

                optimizer.zero_grad()
                outputs = model(data)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()

        # Test on synthetic data
        model.eval()
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for data, labels in test_loader:
                data, labels = data.to(self.device), labels.squeeze().to(self.device)
                outputs = model(data)
                _, predicted = torch.max(outputs, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        accuracy = accuracy_score(all_labels, all_predictions)
        return accuracy * 100  # Return as percentage

    def evaluate_synthetic_data_quality(self, ddpm_model_path, num_samples_per_class=100):
        """
        Comprehensive evaluation of synthetic data quality using GAN-train and GAN-test
        """
        print("Evaluating synthetic data quality...")

        # Generate real data
        print("Generating real data...")
        real_signals, real_labels, _ = create_balanced_dataset(self.config, samples_per_class=200)

        # Split real data
        real_train, real_test, real_train_labels, real_test_labels = train_test_split(
            real_signals, real_labels, test_size=0.5, random_state=42, stratify=real_labels
        )

        # Generate synthetic data
        print("Generating synthetic data...")
        synthetic_signals, synthetic_labels, _ = generate_samples(
            ddpm_model_path, num_samples_per_class=num_samples_per_class, config=self.config
        )

        # Split synthetic data
        synthetic_train, synthetic_test, synthetic_train_labels, synthetic_test_labels = train_test_split(
            synthetic_signals, synthetic_labels, test_size=0.5, random_state=42, stratify=synthetic_labels
        )

        # Run evaluations
        gan_train_score = self.gan_train_evaluation(
            synthetic_train, synthetic_train_labels, real_test, real_test_labels
        )

        gan_test_score = self.gan_test_evaluation(
            real_train, real_train_labels, synthetic_test, synthetic_test_labels
        )

        print(f"GAN-Train Score: {gan_train_score:.2f}%")
        print(f"GAN-Test Score: {gan_test_score:.2f}%")

        return gan_train_score, gan_test_score

def compare_generation_methods(config=Config()):
    """Compare different data generation methods"""

    print("Comparing data generation methods...")

    # Create real test data
    real_signals, real_labels, _ = create_balanced_dataset(config, samples_per_class=100)
    real_train, real_test, real_train_labels, real_test_labels = train_test_split(
        real_signals, real_labels, test_size=0.3, random_state=42, stratify=real_labels
    )

    evaluator = GANTrainTest(config)
    results = {}

    # Evaluate DDPM
    ddpm_model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')
    if os.path.exists(ddpm_model_path):
        print("\nEvaluating Conditional DDPM...")
        gan_train, gan_test = evaluator.evaluate_synthetic_data_quality(ddpm_model_path, num_samples_per_class=50)
        results['Conditional DDPM'] = {'GAN-Train': gan_train, 'GAN-Test': gan_test}
    else:
        print("DDPM model not found. Skipping DDPM evaluation.")

    # You can add other generation methods here for comparison
    # For example: WGAN, CGAN, etc.

    return results

def visualize_evaluation_results(results, save_path=None):
    """Visualize evaluation results"""

    methods = list(results.keys())
    gan_train_scores = [results[method]['GAN-Train'] for method in methods]
    gan_test_scores = [results[method]['GAN-Test'] for method in methods]

    x = np.arange(len(methods))
    width = 0.35

    fig, ax = plt.subplots(figsize=(10, 6))

    bars1 = ax.bar(x - width/2, gan_train_scores, width, label='GAN-Train', alpha=0.8)
    bars2 = ax.bar(x + width/2, gan_test_scores, width, label='GAN-Test', alpha=0.8)

    ax.set_xlabel('Generation Methods')
    ax.set_ylabel('Accuracy (%)')
    ax.set_title('GAN-Train/GAN-Test Evaluation Results')
    ax.set_xticks(x)
    ax.set_xticklabels(methods)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}%',
                   xy=(bar.get_x() + bar.get_width() / 2, height),
                   xytext=(0, 3),  # 3 points vertical offset
                   textcoords="offset points",
                   ha='center', va='bottom')

    for bar in bars2:
        height = bar.get_height()
        ax.annotate(f'{height:.1f}%',
                   xy=(bar.get_x() + bar.get_width() / 2, height),
                   xytext=(0, 3),  # 3 points vertical offset
                   textcoords="offset points",
                   ha='center', va='bottom')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    plt.show()

def analyze_sample_diversity(ddpm_model_path, config=Config(), num_samples=100):
    """Analyze the diversity of generated samples"""

    print("Analyzing sample diversity...")

    # Generate multiple samples for each class
    generated_signals, generated_labels, _ = generate_samples(
        ddpm_model_path, num_samples_per_class=num_samples, config=config
    )

    # Calculate diversity metrics for each class
    diversity_metrics = {}

    for class_idx in range(config.num_classes):
        class_mask = generated_labels == class_idx
        class_samples = generated_signals[class_mask]

        if len(class_samples) > 1:
            # Calculate pairwise distances
            distances = []
            for i in range(len(class_samples)):
                for j in range(i+1, len(class_samples)):
                    dist = np.linalg.norm(class_samples[i] - class_samples[j])
                    distances.append(dist)

            diversity_metrics[config.fault_types[class_idx]] = {
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances),
                'min_distance': np.min(distances),
                'max_distance': np.max(distances)
            }

    # Print diversity analysis
    print("\nSample Diversity Analysis:")
    print("-" * 50)
    for fault_type, metrics in diversity_metrics.items():
        print(f"{fault_type}:")
        print(f"  Mean distance: {metrics['mean_distance']:.4f}")
        print(f"  Std distance: {metrics['std_distance']:.4f}")
        print(f"  Min distance: {metrics['min_distance']:.4f}")
        print(f"  Max distance: {metrics['max_distance']:.4f}")
        print()

    return diversity_metrics

if __name__ == "__main__":
    config = Config()

    # Create directories
    os.makedirs(config.models_dir, exist_ok=True)
    os.makedirs(config.figures_dir, exist_ok=True)

    # Compare generation methods
    print("Running comprehensive evaluation...")
    results = compare_generation_methods(config)

    if results:
        # Visualize results
        viz_path = os.path.join(config.figures_dir, 'evaluation_results.png')
        visualize_evaluation_results(results, viz_path)

        # Print summary
        print("\nEvaluation Summary:")
        print("=" * 50)
        for method, scores in results.items():
            print(f"{method}:")
            print(f"  GAN-Train Score: {scores['GAN-Train']:.2f}%")
            print(f"  GAN-Test Score: {scores['GAN-Test']:.2f}%")
            print()

        # Analyze sample diversity
        ddpm_model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')
        if os.path.exists(ddpm_model_path):
            diversity_metrics = analyze_sample_diversity(ddpm_model_path, config)

    print("Evaluation completed!")
