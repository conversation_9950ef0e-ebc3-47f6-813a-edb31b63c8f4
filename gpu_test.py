"""
GPU Detection and Performance Test Script
"""

import torch
import time
import numpy as np
from config import Config

def check_gpu_availability():
    """Check GPU availability and specifications"""
    
    print("="*60)
    print("GPU AVAILABILITY CHECK")
    print("="*60)
    
    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    print(f"CUDA Available: {cuda_available}")
    
    if cuda_available:
        # GPU count
        gpu_count = torch.cuda.device_count()
        print(f"Number of GPUs: {gpu_count}")
        
        # Current GPU
        current_gpu = torch.cuda.current_device()
        print(f"Current GPU: {current_gpu}")
        
        # GPU specifications
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name}")
            print(f"  Memory: {gpu_memory:.2f} GB")
            
            # Memory usage
            if i == current_gpu:
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                cached = torch.cuda.memory_reserved(i) / 1024**3
                print(f"  Memory Allocated: {allocated:.2f} GB")
                print(f"  Memory Cached: {cached:.2f} GB")
    else:
        print("CUDA is not available. Will use CPU.")
    
    # Device configuration
    config = Config()
    print(f"\nConfigured Device: {config.device}")
    
    return cuda_available

def performance_test():
    """Test performance difference between CPU and GPU"""
    
    print("\n" + "="*60)
    print("PERFORMANCE TEST")
    print("="*60)
    
    # Test parameters
    batch_size = 32
    signal_length = 1024
    num_iterations = 100
    
    # Create test data
    test_data = torch.randn(batch_size, 1, signal_length)
    
    # Test on CPU
    print("Testing on CPU...")
    device_cpu = torch.device('cpu')
    test_data_cpu = test_data.to(device_cpu)
    
    start_time = time.time()
    for _ in range(num_iterations):
        # Simulate some computation
        result = torch.nn.functional.conv1d(test_data_cpu, 
                                          torch.randn(64, 1, 7).to(device_cpu))
        result = torch.nn.functional.relu(result)
        result = torch.nn.functional.adaptive_avg_pool1d(result, 1)
    cpu_time = time.time() - start_time
    
    print(f"CPU Time: {cpu_time:.4f} seconds")
    
    # Test on GPU (if available)
    if torch.cuda.is_available():
        print("Testing on GPU...")
        device_gpu = torch.device('cuda')
        test_data_gpu = test_data.to(device_gpu)
        
        # Warm up GPU
        for _ in range(10):
            result = torch.nn.functional.conv1d(test_data_gpu, 
                                              torch.randn(64, 1, 7).to(device_gpu))
        
        torch.cuda.synchronize()  # Wait for GPU operations to complete
        
        start_time = time.time()
        for _ in range(num_iterations):
            result = torch.nn.functional.conv1d(test_data_gpu, 
                                              torch.randn(64, 1, 7).to(device_gpu))
            result = torch.nn.functional.relu(result)
            result = torch.nn.functional.adaptive_avg_pool1d(result, 1)
        
        torch.cuda.synchronize()  # Wait for GPU operations to complete
        gpu_time = time.time() - start_time
        
        print(f"GPU Time: {gpu_time:.4f} seconds")
        print(f"Speedup: {cpu_time/gpu_time:.2f}x")
        
        # Memory usage
        memory_used = torch.cuda.memory_allocated() / 1024**2
        print(f"GPU Memory Used: {memory_used:.2f} MB")
    else:
        print("GPU not available for testing.")

def test_ddpm_gpu_compatibility():
    """Test DDPM model GPU compatibility"""
    
    print("\n" + "="*60)
    print("DDPM GPU COMPATIBILITY TEST")
    print("="*60)
    
    try:
        from ddpm_model import ConditionalDDPM
        
        config = Config()
        print(f"Testing on device: {config.device}")
        
        # Create model
        model = ConditionalDDPM(config).to(config.device)
        print("✓ Model created successfully")
        
        # Create test data
        batch_size = 4
        test_signals = torch.randn(batch_size, 1, config.sample_length).to(config.device)
        test_labels = torch.randint(0, config.num_classes, (batch_size,)).to(config.device)
        
        print("✓ Test data created")
        
        # Test forward pass
        model.train()
        loss = model(test_signals, test_labels)
        print(f"✓ Forward pass successful, loss: {loss.item():.4f}")
        
        # Test backward pass
        loss.backward()
        print("✓ Backward pass successful")
        
        # Test sampling
        model.eval()
        with torch.no_grad():
            samples = model.sample(2, torch.tensor([0, 1]).to(config.device), config.sample_length)
            print(f"✓ Sampling successful, generated shape: {samples.shape}")
        
        print("\n✅ All DDPM GPU tests passed!")
        
    except Exception as e:
        print(f"❌ DDPM GPU test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def memory_usage_estimate():
    """Estimate memory usage for training"""
    
    print("\n" + "="*60)
    print("MEMORY USAGE ESTIMATION")
    print("="*60)
    
    config = Config()
    
    # Model parameters estimation
    # This is a rough estimate based on typical U-Net architecture
    estimated_params = 2_000_000  # ~2M parameters
    param_memory = estimated_params * 4 / 1024**2  # 4 bytes per float32 parameter
    
    # Training data memory
    batch_size = config.ddpm_batch_size
    signal_length = config.sample_length
    batch_memory = batch_size * signal_length * 4 / 1024**2  # MB
    
    # Gradient memory (same as parameters)
    gradient_memory = param_memory
    
    # Optimizer state (Adam uses 2x parameter memory)
    optimizer_memory = param_memory * 2
    
    # Total estimated memory
    total_memory = param_memory + batch_memory + gradient_memory + optimizer_memory
    
    print(f"Estimated Memory Usage:")
    print(f"  Model Parameters: {param_memory:.2f} MB")
    print(f"  Batch Data: {batch_memory:.2f} MB")
    print(f"  Gradients: {gradient_memory:.2f} MB")
    print(f"  Optimizer State: {optimizer_memory:.2f} MB")
    print(f"  Total Estimated: {total_memory:.2f} MB ({total_memory/1024:.2f} GB)")
    
    if torch.cuda.is_available():
        available_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"  Available GPU Memory: {available_memory:.2f} GB")
        
        if total_memory/1024 < available_memory * 0.8:  # Use 80% as safe threshold
            print("✅ Sufficient GPU memory for training")
        else:
            print("⚠️  May need to reduce batch size or use CPU")

if __name__ == "__main__":
    # Run all tests
    gpu_available = check_gpu_availability()
    performance_test()
    test_ddpm_gpu_compatibility()
    memory_usage_estimate()
    
    print("\n" + "="*60)
    print("GPU TEST SUMMARY")
    print("="*60)
    
    if gpu_available:
        print("✅ GPU is available and ready for training")
        print("🚀 You can now run the training scripts with GPU acceleration")
        print("\nTo train with GPU:")
        print("  python main.py --quick-test  # Quick test")
        print("  python main.py              # Full training")
    else:
        print("ℹ️  GPU not available, will use CPU")
        print("💡 Training will be slower but still functional")
