"""
Improved CNN with deformable convolution and adaptive pooling for fault classification
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from config import Config

class DeformableConv1d(nn.Module):
    """1D Deformable Convolution"""
    
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0, bias=True):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = kernel_size
        self.stride = stride
        self.padding = padding
        
        # Regular convolution for generating offsets
        self.offset_conv = nn.Conv1d(in_channels, kernel_size, kernel_size, stride, padding, bias=True)
        
        # Regular convolution for features
        self.regular_conv = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding, bias=bias)
        
        # Initialize offset conv to output zeros
        nn.init.constant_(self.offset_conv.weight, 0.)
        nn.init.constant_(self.offset_conv.bias, 0.)
    
    def forward(self, x):
        # Generate offsets
        offsets = self.offset_conv(x)
        
        # Apply deformable convolution (simplified version)
        # In practice, this would involve more complex offset sampling
        # For 1D signals, we'll use a simplified approach
        
        # Get regular convolution output
        regular_out = self.regular_conv(x)
        
        # Apply learned offsets as a form of attention
        # This is a simplified implementation
        offset_weights = torch.sigmoid(offsets)
        
        # Interpolate features based on offsets
        batch_size, channels, length = regular_out.shape
        
        # Create a simple offset-based modulation
        if offset_weights.shape[2] == regular_out.shape[2]:
            modulated_out = regular_out * offset_weights.mean(dim=1, keepdim=True)
        else:
            # Adaptive pooling to match dimensions
            offset_weights = F.adaptive_avg_pool1d(offset_weights, regular_out.shape[2])
            modulated_out = regular_out * offset_weights.mean(dim=1, keepdim=True)
        
        return modulated_out

class AdaptivePooling1d(nn.Module):
    """Adaptive pooling combining average and max pooling"""
    
    def __init__(self, output_size):
        super().__init__()
        self.output_size = output_size
        self.adaptive_avg_pool = nn.AdaptiveAvgPool1d(output_size)
        self.adaptive_max_pool = nn.AdaptiveMaxPool1d(output_size)
        
        # Learnable combination weights
        self.alpha = nn.Parameter(torch.tensor(0.5))
    
    def forward(self, x):
        avg_pooled = self.adaptive_avg_pool(x)
        max_pooled = self.adaptive_max_pool(x)
        
        # Combine with learnable weight
        combined = self.alpha * avg_pooled + (1 - self.alpha) * max_pooled
        return combined

class ImprovedCNN(nn.Module):
    """Improved CNN with deformable convolution and adaptive pooling"""
    
    def __init__(self, config=Config()):
        super().__init__()
        self.config = config
        
        # First regular convolution layer
        self.conv1 = nn.Conv1d(1, config.cnn_channels[0], kernel_size=config.cnn_kernel_sizes[0], padding=3)
        self.bn1 = nn.BatchNorm1d(config.cnn_channels[0])
        
        # Second regular convolution layer
        self.conv2 = nn.Conv1d(config.cnn_channels[0], config.cnn_channels[1], 
                              kernel_size=config.cnn_kernel_sizes[1], padding=2)
        self.bn2 = nn.BatchNorm1d(config.cnn_channels[1])
        
        # Deformable convolution layer
        self.deform_conv = DeformableConv1d(config.cnn_channels[1], config.cnn_channels[2], 
                                          kernel_size=config.cnn_kernel_sizes[2], padding=1)
        self.bn3 = nn.BatchNorm1d(config.cnn_channels[2])
        
        # Fourth regular convolution layer
        self.conv4 = nn.Conv1d(config.cnn_channels[2], config.cnn_channels[3], 
                              kernel_size=config.cnn_kernel_sizes[3], padding=1)
        self.bn4 = nn.BatchNorm1d(config.cnn_channels[3])
        
        # Adaptive pooling
        self.adaptive_pool = AdaptivePooling1d(output_size=32)
        
        # Dropout
        self.dropout = nn.Dropout(config.cnn_dropout)
        
        # Fully connected layers
        self.fc1 = nn.Linear(config.cnn_channels[3] * 32, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, config.num_classes)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Add channel dimension if needed
        if len(x.shape) == 2:
            x = x.unsqueeze(1)
        
        # First conv block
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.max_pool1d(x, kernel_size=2)
        
        # Second conv block
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.max_pool1d(x, kernel_size=2)
        
        # Deformable conv block
        x = F.relu(self.bn3(self.deform_conv(x)))
        x = F.max_pool1d(x, kernel_size=2)
        
        # Fourth conv block
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.max_pool1d(x, kernel_size=2)
        
        # Adaptive pooling
        x = self.adaptive_pool(x)
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        # Fully connected layers
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

class BasicCNN(nn.Module):
    """Basic CNN for comparison"""
    
    def __init__(self, config=Config()):
        super().__init__()
        self.config = config
        
        self.conv1 = nn.Conv1d(1, 32, kernel_size=7, padding=3)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=5, padding=2)
        self.conv3 = nn.Conv1d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv1d(128, 256, kernel_size=3, padding=1)
        
        self.pool = nn.MaxPool1d(2)
        self.adaptive_pool = nn.AdaptiveAvgPool1d(32)
        self.dropout = nn.Dropout(config.cnn_dropout)
        
        self.fc1 = nn.Linear(256 * 32, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, config.num_classes)
    
    def forward(self, x):
        if len(x.shape) == 2:
            x = x.unsqueeze(1)
        
        x = F.relu(self.conv1(x))
        x = self.pool(x)
        
        x = F.relu(self.conv2(x))
        x = self.pool(x)
        
        x = F.relu(self.conv3(x))
        x = self.pool(x)
        
        x = F.relu(self.conv4(x))
        x = self.pool(x)
        
        x = self.adaptive_pool(x)
        x = x.view(x.size(0), -1)
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def count_parameters(model):
    """Count the number of trainable parameters"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def test_models():
    """Test the models with dummy data"""
    config = Config()
    
    # Create models
    improved_cnn = ImprovedCNN(config)
    basic_cnn = BasicCNN(config)
    
    # Create dummy input
    batch_size = 8
    signal_length = config.sample_length
    x = torch.randn(batch_size, signal_length)
    
    print("Testing models...")
    print(f"Input shape: {x.shape}")
    
    # Test improved CNN
    with torch.no_grad():
        output_improved = improved_cnn(x)
        print(f"Improved CNN output shape: {output_improved.shape}")
        print(f"Improved CNN parameters: {count_parameters(improved_cnn):,}")
    
    # Test basic CNN
    with torch.no_grad():
        output_basic = basic_cnn(x)
        print(f"Basic CNN output shape: {output_basic.shape}")
        print(f"Basic CNN parameters: {count_parameters(basic_cnn):,}")
    
    print("Models tested successfully!")

if __name__ == "__main__":
    test_models()
