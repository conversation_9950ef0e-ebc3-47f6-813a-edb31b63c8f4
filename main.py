"""
Main script to run the complete pipeline:
1. Generate synthetic bearing vibration data
2. Train Conditional DDPM for data augmentation
3. Train improved CNN classifier
4. Evaluate using GAN-train/GAN-test methods
5. Generate comprehensive results and visualizations
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt
import torch

from config import Config
from data_generator import visualize_signals, create_balanced_dataset, create_imbalanced_dataset
from train_ddpm import train_ddpm, generate_samples, visualize_generated_samples
from train_classifier import experiment_with_augmentation
from evaluation import compare_generation_methods, visualize_evaluation_results, analyze_sample_diversity
from improved_cnn import ImprovedCNN, BasicCNN, count_parameters

def setup_directories(config):
    """Create necessary directories"""
    directories = [
        config.data_dir,
        config.models_dir,
        config.results_dir,
        config.figures_dir
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def run_data_generation_demo(config):
    """Demonstrate data generation capabilities"""
    print("\n" + "="*60)
    print("STEP 1: DATA GENERATION DEMONSTRATION")
    print("="*60)

    # Visualize example signals
    print("Generating example signals for each fault type...")
    signal_viz_path = os.path.join(config.figures_dir, 'signal_examples.png')
    visualize_signals(config, signal_viz_path)

    # Create balanced dataset
    print("Creating balanced dataset...")
    balanced_signals, balanced_labels, _ = create_balanced_dataset(config, samples_per_class=100)
    print(f"Balanced dataset shape: {balanced_signals.shape}")

    # Create imbalanced datasets
    for ratio in config.imbalance_ratios:
        print(f"Creating imbalanced dataset with ratio 1:{ratio}...")
        imb_signals, imb_labels, _ = create_imbalanced_dataset(config, majority_samples=200, imbalance_ratio=ratio)

        # Print class distribution
        unique, counts = np.unique(imb_labels, return_counts=True)
        print(f"  Class distribution for ratio 1:{ratio}:")
        for class_idx, count in zip(unique, counts):
            print(f"    {config.fault_types[class_idx]}: {count} samples")

    print("Data generation demonstration completed!")

def run_ddpm_training(config):
    """Train the Conditional DDPM model"""
    print("\n" + "="*60)
    print("STEP 2: CONDITIONAL DDPM TRAINING")
    print("="*60)

    ddpm_model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')

    if os.path.exists(ddpm_model_path):
        print(f"DDPM model already exists at {ddpm_model_path}")
        response = input("Do you want to retrain? (y/n): ")
        if response.lower() != 'y':
            print("Skipping DDPM training...")
            return ddpm_model_path

    print("Training Conditional DDPM...")
    model, losses = train_ddpm(config, ddpm_model_path)

    # Generate and visualize samples
    print("Generating sample synthetic data...")
    generated_signals, generated_labels, _ = generate_samples(ddpm_model_path, num_samples_per_class=20, config=config)

    viz_path = os.path.join(config.figures_dir, 'generated_samples_demo.png')
    visualize_generated_samples(generated_signals, generated_labels, config, viz_path)

    print("DDPM training completed!")
    return ddpm_model_path

def run_classifier_experiments(config, ddpm_model_path):
    """Run classifier experiments with different imbalance ratios"""
    print("\n" + "="*60)
    print("STEP 3: CLASSIFIER EXPERIMENTS")
    print("="*60)

    # Test model architectures first
    print("Testing model architectures...")
    improved_cnn = ImprovedCNN(config)
    basic_cnn = BasicCNN(config)

    print(f"Improved CNN parameters: {count_parameters(improved_cnn):,}")
    print(f"Basic CNN parameters: {count_parameters(basic_cnn):,}")

    # Run experiments for each imbalance ratio
    all_results = {}

    for ratio in config.imbalance_ratios:
        print(f"\nRunning experiment with imbalance ratio 1:{ratio}...")

        try:
            result = experiment_with_augmentation(config, ratio)
            all_results[ratio] = result

            print(f"Results for ratio 1:{ratio}:")
            print(f"  Without augmentation: {result['acc_no_aug']:.2f}%")
            print(f"  With augmentation: {result['acc_with_aug']:.2f}%")
            print(f"  Improvement: {result['improvement']:.2f}%")

        except Exception as e:
            print(f"Error in experiment with ratio 1:{ratio}: {e}")
            continue

    # Create summary plot
    if all_results:
        create_summary_plot(all_results, config)

    print("Classifier experiments completed!")
    return all_results

def run_evaluation(config, ddpm_model_path):
    """Run comprehensive evaluation using GAN-train/GAN-test"""
    print("\n" + "="*60)
    print("STEP 4: COMPREHENSIVE EVALUATION")
    print("="*60)

    try:
        # Compare generation methods
        print("Running GAN-train/GAN-test evaluation...")
        results = compare_generation_methods(config)

        if results:
            # Visualize results
            viz_path = os.path.join(config.figures_dir, 'evaluation_results.png')
            visualize_evaluation_results(results, viz_path)

            # Print summary
            print("\nEvaluation Summary:")
            print("-" * 30)
            for method, scores in results.items():
                print(f"{method}:")
                print(f"  GAN-Train Score: {scores['GAN-Train']:.2f}%")
                print(f"  GAN-Test Score: {scores['GAN-Test']:.2f}%")

        # Analyze sample diversity
        print("\nAnalyzing sample diversity...")
        diversity_metrics = analyze_sample_diversity(ddpm_model_path, config)

        print("Evaluation completed!")
        return results, diversity_metrics

    except Exception as e:
        print(f"Error in evaluation: {e}")
        return None, None

def create_summary_plot(all_results, config):
    """Create summary plot of all experiments"""

    ratios = list(all_results.keys())
    acc_no_aug = [all_results[ratio]['acc_no_aug'] for ratio in ratios]
    acc_with_aug = [all_results[ratio]['acc_with_aug'] for ratio in ratios]
    improvements = [all_results[ratio]['improvement'] for ratio in ratios]

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Accuracy comparison
    x = np.arange(len(ratios))
    width = 0.35

    ax1.bar(x - width/2, acc_no_aug, width, label='Without Augmentation', alpha=0.8)
    ax1.bar(x + width/2, acc_with_aug, width, label='With Augmentation', alpha=0.8)

    ax1.set_xlabel('Imbalance Ratio (1:X)')
    ax1.set_ylabel('Accuracy (%)')
    ax1.set_title('Classification Accuracy Comparison')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'1:{ratio}' for ratio in ratios])
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Improvement plot
    ax2.bar(range(len(ratios)), improvements, alpha=0.8, color='green')
    ax2.set_xlabel('Imbalance Ratio (1:X)')
    ax2.set_ylabel('Improvement (%)')
    ax2.set_title('Accuracy Improvement with Data Augmentation')
    ax2.set_xticks(range(len(ratios)))
    ax2.set_xticklabels([f'1:{ratio}' for ratio in ratios])
    ax2.grid(True, alpha=0.3)

    # Add value labels
    for i, v in enumerate(improvements):
        ax2.text(i, v + 0.1, f'{v:.1f}%', ha='center', va='bottom')

    plt.tight_layout()

    summary_path = os.path.join(config.figures_dir, 'experiment_summary.png')
    plt.savefig(summary_path, dpi=config.dpi, bbox_inches='tight')
    plt.show()

    print(f"Summary plot saved to {summary_path}")

def generate_final_report(config, classifier_results, evaluation_results, diversity_metrics):
    """Generate a final comprehensive report"""

    report_path = os.path.join(config.results_dir, 'final_report.txt')

    with open(report_path, 'w') as f:
        f.write("CONDITIONAL DDPM AND IMPROVED CNN FOR BEARING FAULT DIAGNOSIS\n")
        f.write("=" * 60 + "\n\n")

        f.write("CONFIGURATION:\n")
        f.write("-" * 20 + "\n")
        f.write(f"Device: {config.device}\n")
        f.write(f"Sample length: {config.sample_length}\n")
        f.write(f"Sampling rate: {config.sampling_rate} Hz\n")
        f.write(f"Number of classes: {config.num_classes}\n")
        f.write(f"DDPM timesteps: {config.ddpm_timesteps}\n")
        f.write(f"DDPM epochs: {config.ddpm_epochs}\n")
        f.write(f"CNN epochs: {config.cnn_epochs}\n\n")

        if classifier_results:
            f.write("CLASSIFIER EXPERIMENT RESULTS:\n")
            f.write("-" * 30 + "\n")
            for ratio, result in classifier_results.items():
                f.write(f"Imbalance ratio 1:{ratio}:\n")
                f.write(f"  Without augmentation: {result['acc_no_aug']:.2f}%\n")
                f.write(f"  With augmentation: {result['acc_with_aug']:.2f}%\n")
                f.write(f"  Improvement: {result['improvement']:.2f}%\n\n")

        if evaluation_results:
            f.write("GAN-TRAIN/GAN-TEST EVALUATION:\n")
            f.write("-" * 30 + "\n")
            for method, scores in evaluation_results.items():
                f.write(f"{method}:\n")
                f.write(f"  GAN-Train Score: {scores['GAN-Train']:.2f}%\n")
                f.write(f"  GAN-Test Score: {scores['GAN-Test']:.2f}%\n\n")

        if diversity_metrics:
            f.write("SAMPLE DIVERSITY ANALYSIS:\n")
            f.write("-" * 30 + "\n")
            for fault_type, metrics in diversity_metrics.items():
                f.write(f"{fault_type}:\n")
                f.write(f"  Mean distance: {metrics['mean_distance']:.4f}\n")
                f.write(f"  Std distance: {metrics['std_distance']:.4f}\n\n")

    print(f"Final report saved to {report_path}")

def main():
    """Main function to run the complete pipeline"""

    parser = argparse.ArgumentParser(description='Conditional DDPM and Improved CNN for Bearing Fault Diagnosis')
    parser.add_argument('--skip-ddpm', action='store_true', help='Skip DDPM training')
    parser.add_argument('--skip-classifier', action='store_true', help='Skip classifier experiments')
    parser.add_argument('--skip-evaluation', action='store_true', help='Skip evaluation')
    parser.add_argument('--quick-test', action='store_true', help='Run quick test with reduced parameters')

    args = parser.parse_args()

    # Initialize configuration
    config = Config()

    # Modify config for quick test
    if args.quick_test:
        config.ddpm_epochs = 10
        config.cnn_epochs = 10
        config.imbalance_ratios = [4]  # Test only one ratio
        print("Running in quick test mode with reduced parameters...")

    print("Starting Conditional DDPM and Improved CNN Pipeline...")
    print(f"Using device: {config.device}")

    # Setup directories
    setup_directories(config)

    # Step 1: Data generation demonstration
    run_data_generation_demo(config)

    # Step 2: Train DDPM
    ddpm_model_path = None
    if not args.skip_ddpm:
        ddpm_model_path = run_ddpm_training(config)
    else:
        ddpm_model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')
        if not os.path.exists(ddpm_model_path):
            print("DDPM model not found. Please train DDPM first.")
            return

    # Step 3: Classifier experiments
    classifier_results = None
    if not args.skip_classifier and ddpm_model_path:
        classifier_results = run_classifier_experiments(config, ddpm_model_path)

    # Step 4: Evaluation
    evaluation_results = None
    diversity_metrics = None
    if not args.skip_evaluation and ddpm_model_path:
        evaluation_results, diversity_metrics = run_evaluation(config, ddpm_model_path)

    # Generate final report
    generate_final_report(config, classifier_results, evaluation_results, diversity_metrics)

    print("\n" + "="*60)
    print("PIPELINE COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"Results saved in: {config.results_dir}")
    print(f"Figures saved in: {config.figures_dir}")
    print(f"Models saved in: {config.models_dir}")

if __name__ == "__main__":
    main()
