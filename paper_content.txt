
--- Page 1 ---
IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025 3517712
Data Augmentation Fault Diagnosis of Rolling
Machinery Using Condition Denoising Diffusion
Probabilistic Model and Improved CNN
<PERSON>
 ,Member, IEEE , Tianming <PERSON>g
 , and <PERSON><PERSON>
 ,Member, IEEE
Abstract — Imbalanced data present a significant challenge in
intelligent fault diagnosis due to limited sample availability, and
various generative models have been proposed for data augmen-
tation. However, these training processes are often complex, prone
to model collapse, and fail to improve the classifier’s ability to
categorize fault signals. Consequently, a bearing fault diagnosis
method based on condition denoising diffusion probabilistic
model (DDPM) and improved convolution neural network (CNN)
is proposed to address class-imbalanced. First, the condition
DDPM is introduced to generate high-quality and diverse vibra-
tion synthetic data through accurate physical simulation. Second,
the synthetic data’s effectiveness is assessed using the proposed
improved CNN, which offers enhanced flexibility in perception.
Third, the GAN-train/GAN-test is used to provide a compre-
hensive evaluation method for synthetic data and indicate the
diversity and similarity of synthetic sample. Finally, two machin-
ery datasets, five classifier models and seven data augmentation
methods are compared under varying imbalance ratios of 1:2, 1:4,
and 1:10. These results confirm the sample generation capabilities
of condition DDPM and the high classification performance of
improved CNN.
Index Terms— Condition denoising diffusion probabilistic
model (DDPM), data augmentation, deep learning, fault diag-
nosis, imbalanced data, improved convolution neural network
(CNN).
I. I NTRODUCTION
IN
MODERN industries, rolling bearings are indispensable
components in various mechanical systems [1]. These
bearings are typically subject to harsh operating conditions,
which lead to degradation or malfunction [2]. Consequently,
implementing effective fault diagnosis is essential for ensuring
the reliable daily operation and maintenance of machines [3].
Traditional fault diagnosis, which requires signal processing
to extract features for machine-learning analysis, struggles to
maintain accuracy and effectiveness in environments where
machine data are growing exponentially [4],[5].
With the increase in computer arithmetic and the develop-
ment of deep learning algorithms, intelligent fault diagnosis is
Received 26 November 2024; revised 16 January 2025; accepted
3 February 2025. Date of publication 25 February 2025; date of current
version 21 March 2025. This work was supported in part by the National
Natural Science Foundation of China under Grant 52377111 and in part by
the Chunhui Program by the Ministry of Education of China under Grant
HZKY20220084. The Associate Editor coordinating the review process was
Dr. Paolo Castello. (Corresponding author: Dongdong Li.)
The authors are with the College of Electrical Engineering, Shanghai Uni-
versity of Electric Power, Shanghai 200090, China (e-mail: nihaozhaoyao@
163.com; <EMAIL>; <EMAIL>).
Digital Object Identifier 10.1109/TIM.2025.3545721proposed to fully process and analyze data, providing accurate
diagnostic results [6]. The intelligent algorithms capitalize on
the inherent relevant features of signals and eliminate the
need for manual feature selection in machine learning [7].
Recent research has extensively demonstrated that the superior
performance of deep learning-based techniques. For instance,
Zhang et al. [8]proposed a novel fault diagnosis method
based on local binary temporal convolutional neural network
(LBTCNN) to address the issue of overfitting due to limited
faulty data. Niu et al. [9]adopted a channel attention mod-
ule and a nonlocal attention module to improve the feature
learning ability of deep residual convolution neural network
(CNN), utilizing information fusion from multiple sensor
data. Chen et al. [10] proposed a multigrained hybrid neural
network using short-time Fourier transform for time-frequency
image conversion and incorporating multigrained feature rep-
resentation and depthwise wave blocks to improve fault
diagnosis accuracy and robustness.
However, the aforementioned structure of CNNs is complex,
with multiple modules designed to enhance feature learning
ability. Additionally, it assumes that the training set contains
sufficient data for different categories, which is unrealistic in
industrial applications. In engineering scenarios, the number
of healthy class samples outweighs those from other fault
classes, leading to the model classifying all test samples into
the dominant class [11]. To address this issue, algorithm-based
and data-based are two main method to solve imbalanced fault
diagnosis [12]. The former methods involve modifying the
learning algorithms to better handle class imbalance. However,
these methods are often customized for specific problems
and tend to lack portability [13]. In contrast, methods that
address the imbalance by modifying the dataset itself are
generally more versatile and widely applicable. From a data
perspective, techniques such as data oversampling and data
generation are primarily used for data augmentation [14]. The
synthetic minority oversampling technique (SMOTE) serves as
a foundational approach to data oversampling. Building on this
technique, subsequent algorithms such as adaptive synthetic
sampling (ADASYN) [15] and SMOTEENN [16], which inte-
grates both oversampling and undersampling methods, have
been developed to enhance its effectiveness further. However,
SMOTE does not fully account for the true data distribution,
which may lead to a potential loss of information in the
dataset.
1557-9662 © 2025 IEEE. All rights reserved, including rights for text and data mining, and training of artificial intelligence
and similar technologies. Personal use is permitted, but republication/redistribution requires IEEE permission.
See https://www.ieee.org/publications/rights/index.html for more information.
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 2 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
Thus, data augmentation techniques based on genera-
tive models have gradually become a primary method for
addressing data imbalance problems [17]. The mainstream
generative models commonly used for fault diagnosis are
variational autoencoders (V AEs) and generative adversar-
ial networks (GANs). V AEs take a probabilistic approach,
where the encoder outputs a latent space distribution,
allowing sampling during decoding to generate new data
instances. Zhao et al. [18] proposed the normalized condi-
tional variational auto-encoder with adaptive focal loss for
mechanical fault diagnosis under class-imbalanced data con-
ditions. However, since V AE training relies on a hypothetical
loss function and Kullback–Leibler divergence to approximate
the true distribution, its generated outputs can sometimes
appear overly smooth and less realistic compared. In contrast,
GAN can learn the data distribution characteristics of input
samples through generator and discriminator, which allows
GAN to generate realistic samples [19]. Huo et al. [20] devel-
oped a new model combining residual mixed self-attention
and Wasserstein GAN with a 1-D CNN for industrial bear-
ing fault diagnosis, effectively addressing data imbalance
and improving classification performance. Dai et al. [21]
developed a categorical feature GAN to generate synthetic
samples for balancing dataset, improving sample quality
and diversity through integrated autoencoder and ACGAN
technologies. Sun et al. [22] proposed a novel approach to
cross-domain data augmentation based on the envelope order
spectrum, utilizing an enhanced variational autoencoder gener-
ative adversarial network (V AEGAN) to address the challenge
of insufficient labeled training data. Although these data
augmentation-based methods have indeed improved the diag-
nosis accuracy to some extent, their reliance on game theory
principles, which can lead to unstable model training and
potential model collapse. Approaches such as the Wasserstein
GAN and its variant with WGAN-GP have been developed to
address issues like mode collapse, they still fall short in terms
of diversity, and need to carefully selected hyperparameters
and regularizers. Furthermore, the intermediate processes of
these methods are not clearly articulated through mathematical
formulation.
Given the limitations of existing generative models, the
diffusion model [23] presents a novel generative approach
characterized by more intuitive mathematical principles and
a clear probabilistic framework for generating high-quality
and diverse sample distributions, despite requiring significant
computational resources. Furthermore, it incorporate parame-
terized Markov processes to enhance computational efficiency
and improve the interpretability of the results. Several studies
have been proposed to assess the effectiveness of diffusion
model. Yang et al. [24] introduced a diffusion model-based
method for fault diagnosis that handles imbalanced data by
generating stable, high-quality samples and proposed a com-
prehensive evaluation framework for thorough performance
assessment. Zhang et al. [25] introduced the interpretable vec-
tor quantization-guided latent denoising diffusion probabilistic
model (DDPM) for machinery fault diagnosis with limited data
availability.Although DDPM can be applied to fault diagnosis with
unbalanced samples, it often results in the underutilization
of labeling information. The training process typically gen-
erates samples based on fault types in separate sessions,
which does not fully exploit the interrelations of labeling
information across different fault types. Furthermore, most
generative models often overlook the performance of classifier
model. Thus, this article introduces condition guidance to
traditional DDPM, leading to more accurate model training
and more effective sample generation process. Additionally,
the integration of deformable concepts with traditional CNN is
proposed to improve the model’s feature learning capabilities.
Ultimately, a comprehensive approach to generate data quality
assessments is presented. The contributions of this article can
be summarized as follows.
1) The condition DDPM is presented to address imbalanced
data in fault diagnosis. It incorporates label information
during the training and sample generation procedures,
generating more realistic and diverse synthetic data with
the guidance.
2) An improved CNN with deformable convolution is
proposed, which enhances the perceptual range by cal-
culating feature offsets and increasing the number of
channels. Additionally, the adaptive pooling is incorpo-
rated to reduce interference from irrelevant features. The
proposed classifier model fully leverages the inherent
distinguishing features of vibration signals.
3) A comprehensive assessment of the synthetic data is
conducted on two machinery datasets, five classifier
models, and seven data augmentation methods under
varying degree of unbalanced. The results demonstrate
that the proposed model can generate high-quality and
diversity samples, achieving accurate fault diagnosis.
The article is structured as follows. Section IIcovers
the basics of the proposed data augmentation and classifier
model. Section IIIdetails the model’s structure and parameters.
Section IVdescribes the experimental setup used to test the
method’s effectiveness. Section Vpresents the ablation exper-
iment and the visual analysis of the generated sample. Finally,
Section VIpresents the conclusion and future directions.
II. P RELIMINARY KNOWLEDGE
A. Condition DDPM
DDPM is inspired by the physics principle of nonequilib-
rium thermodynamics. It consists two main parts: a forward
phase that adds Gaussian noise to the data, and a reverse
phase that denoises and reconstructs the original structure [26].
However, traditional DDPM lacks control over the type of
data generated, making it difficult to handle multiple fault
types effectively. Therefore, the condition DDPM [27] was
proposed, embedding label information into the model training
procedure to generate accurate samples.
The forward propagation is consistent with DDPM, and
this transformation is achieved by defining a Markov chain q
where each step trelies solely on the current data state
and a predetermined variance schedule β. By controlling the
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 3 ---
ZHAO et al.: DATA AUGMENTATION FAULT DIAGNOSIS OF ROLLING MACHINERY USING CONDITION DDPM 3517712
variance of the noise added at each step, the signal is gradually
transformed into a latent representation x1,x2, . . . , xTthat is
eventually indistinguishable from pure noise.
Given a data distribution x0∼q(x0), the process can be
expressed as follows:
q(xt|xt−1)=N/parenleftig
xt;/radicalbig
1−βtxt−1, βtI/parenrightig
q(x1, . . . , xT|x0)=T/productdisplay
t=1q(xt|xt−1) (1)
where xtis the state of data at time t,N(·)is Gaussian
distribution at each time step with variance βt∈(0,1),Tis the
number of diffusion steps, Iis the identity matrix, matching
the dimensions of deviation βt.
With a reparameterization trick: αt=1−βtandαt=/producttextt
i=1αi,(1)can be rewrite as
xt∼q(xt|x0)=N/parenleftig
xt;/radicalbig
αtx0,(1−αt)I/parenrightig
(2)
xt=/radicalbig
αtx0+/radicalbig
1−αtϵ (3)
where ϵ∈N(0,I).
The reverse diffusion process generates data from the latent
space xTand counteract the previously added noise. However,
the true distribution q(xt−1|xt,y)is typically unknown and
computationally infeasible. So, a neural network pθis utilized
to approximate the distribution by pθ(xt−1|xt,y), allowing the
model to focus on parameterizing just the mean µθ(xt,t,y)
and variance 6θ(xt,t,y)to perform reverse diffusion. This
allows for recreation of a data point reflective of original
distribution from noise-infused latent state
pθ(xt−1|xt,y)=N(xt−1;µθ(xt,t,y), 6θ(xt,t,y)).(4)
Based on the Markov chain and Bayesian formula,
q(xt−1|xt)can be reformulated to include x0
q(xt−1|xt,x0)=N/parenleftbig
xt−1; ˜µ(xt,x0),˜βtI/parenrightbig
(5)
where
˜βt=1−αt−1
1−αtβt (6)
˜µt(xt,x0)=√αt−1βt
1−αtx0+√αt(1−αt−1)
1−αtxt (7)
x0=1√αt/parenleftig
xt−/radicalbig
1−αtϵt/parenrightig
. (8)
Thus, the model can be trained by the combination of pand
qvia variational lower bound on the negative log-likelihood
LV L B=Eq(x0:T)/bracketleftbigg
logq(X1:T|X0)
pθ(X0:T)/bracketrightbigg
. (9)
Considering LV L B is analytically computable, a neural
network can be employed to estimate, which serves as an
approximation of ϵ. and by integrating (5)–(8), (9)can be
simplified to
Lsimple
t=Ex0,t,y,ϵ/bracketleftig
∥ϵ−ϵθ/parenleftig/radicalbig
αtx0+/radicalbig
1−αtϵ,t,y/parenrightig
∥2/bracketrightig
(10)
where tis the uniform between 1 and T,yis the label
information.
Fig. 1. Illustration of deformable convolution and AdaptivePool. (a) Defor-
m-conv. (b) AdaptivePool.
During the training period, both conditional and uncondi-
tional information are trained together. The conditional model
is trained with actual label message, whereas the unconditional
model is trained by randomly selecting labels, some of which
are replaced with a null token. This approach allows diffusion
model to balance diversity and consistency effectively.
B. Improved CNN
Inspired by earlier studies [28], this study modifies the
basic CNN architecture by increasing the number of channels
and incorporating a deformable convolution module [29].
The deformable convolution allows the network to adaptively
adjust its receptive field, which helps capture irregular signal
patterns, improving generalization to different fault types.
To further refine the model, an adaptive pooling layer is
employed to reduce redundant feature interactions, effectively
decreasing parameter count and enhancing the network’s
ability to generalize by adjusting the feature map size.
1) Deform Block: This novel convolutional method involves
adding offsets to standard grid sampling locations used in
traditional convolution. As shown in Fig. 1(a), this crucial
adjustment not only allows for free-form deformation of
sampling grid, but also ensures that these deformations are
dynamically learned from preceding feature maps through
additional convolutional layers. This modification enhances the
interaction between the convolutional filters and the 1-D input
data, significantly improving the flexibility and effectiveness
of feature extraction.
2) Adaptive Pooling Block: This block comprises adap-
tiveavgpool and adaptivemaxpool, which dynamically adjust
pooling windows to meet the target output length, as shown
in Fig. 1(b). adaptiveavgpool smooths the input signal by
averaging the values within each window, whereas the other
retains the most prominent features by selecting the maximum
values. This block is effectively reduces the computational
complexity associated with an increasing number of channels,
ensuring the model remains efficient and robust.
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 4 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
Fig. 2. Framework of proposed data augmentation and classifier model (condition DDPM-improved CNN).
C. Quality Evaluation Method
Traditional methods of data quality evaluating, such as
visual inspection and metrics like Inception Score or Frechet
Inception Distance, are often designed for 2-D picture.
However, these methods are not directly applicable to vibration
signals, which have different characteristics and requirements.
To comprehensively evaluate the quality of generated pseu-
dosamples, this article introduces GAN-train/GAN-test [30]
to evaluate the feature learning ability of improved CNN and
the diversity of generated samples.
1) GAN-Train: This method is an evaluation metric that
involves training a classifier on generated samples and then
testing it on real samples. The classifier’s performance on real
samples indicates the quality of generated data, in which a high
GAN-train score means the generated samples are diverse and
realistic enough to help classifier generalize well to real data
distribution.
2) GAN-Test: This method is an evaluation metric that
involves training a classifier on real samples and then testing
it on generated samples, which is the reverse process of
GAN-train. The classifier’s performance on these generated
samples indicates sample’s realism, in which a high GAN-testscore means the generated samples are realistic enough for
classifier, trained on real data, to recognize them accurately.
GAN-train and GAN-test are complementary evaluation met-
rics for assessing the performance of diffusion model. These
methods ensure that generated samples can be applied to
address class-imbalanced by quantifying the diversity and
realism of the synthetic data.
III. P ROPOSED METHODOLOGY
A. Overview of the Proposed Method
The flowchart of proposed method for imbalanced intelli-
gent fault diagnosis is shown in Fig. 2. It consists of three
components: data preprocessing, data augmentation, and fault
classification.
The overall workflow is as follows.
1) Vibration signals acquired by the sensor are sampled
using a sliding window, followed by min–max normal-
ization to maintain a consistent scale for subsequent
analysis.
2) Overlapping signals are employed to train condition
DDPM, and backpropagation is utilized to generate
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 5 ---
ZHAO et al.: DATA AUGMENTATION FAULT DIAGNOSIS OF ROLLING MACHINERY USING CONDITION DDPM 3517712
synthetic samples under the guidance of label informa-
tion, resulting in a mixed dataset with real samples.
3) The mixed dataset is then used to train the improved
convolutional neural network under various ratios of
imbalance, with GAN-train/GAN-test methods applied
to evaluate the quality of synthetic samples.
B. Condition DDPM Model
As described in Fig. 2, the condition DDPM introduces
category labels to guide model training process and fit data
distribution of multiple fault types simultaneously. Without
changing the DDPM structure, the signal denoising direction
is guided based on category information. Data augmentation
involves two steps: training process and inverse data aug-
mentation. During the training process, normalized vibration
signals from fault samples are used to generate a sequence
of samples over time t. A U-Net architecture, as part of
condition DDPM, is employed to estimate the noise added
during forward process, guided by label information. The loss
function is used to compute gradient information of this model
according to (10), ensuring accurate estimation and correction
of noise distribution.
In the inverse sample augmentation process, a noisy vibra-
tion sample is drawn from standard Gaussian distribution,
and then U-Net incorporates label information to guide the
extracted noise sample. In the iterative process from Tto 0,
the noise in the sample is estimated and iteratively removed
to obtain a synthetic sample x0.
ResBlocks are connected in series to form the U-Net
network, where each block processes input features while
incorporating time and label embeddings. The process involves
group normalization, Swish activation, convolution, and
dropout, followed by a residual connection to retain original
features. This design ensures effective integration of temporal
and label information, while preserving gradient flow for stable
training.
C. Improved-CNN Classifier
During the fault diagnosis stage, an improved 1D-CNN
with deformable convolution for time-series vibration signals
is proposed to perform multiclass fault diagnosis using the
generated balanced dataset. The parameter can be seen in
Fig. 2, and the architecture consists of four convolutional
layers, including a deformable convolutional layer, followed
by an adaptive pooling layer and a fully connected layer. Batch
normalization and ReLU activations are used throughout the
network. The network utilizes forward and back propagation
for training, ultimately outputting predicted fault classes.
This design leverages the strengths of convolutional and
deformable convolutional layers, along with adaptive pooling,
to accurately classify faults from vibration signals.
IV. F AULT RECOGNITION OF ROLLING BEARING
Experiments were conducted on a computer with an Intel
Core i3-12100F CPU and an NVIDIA GeForce RTX 3060 Ti
GPU. The system had 32 GB of RAM, and the software
environment included Python 3.11.9 and PyTorch 2.0.1.
Fig. 3. Test rig of PMSM health monitoring.
TABLE I
DETAILS OF BEARING DATASET FROM PMSM P LATFORM
A. Experimental Data Collection
The rolling bearing dataset was obtained from a permanent
magnet synchronous motor (PMSM) fault simulation test rig
in laboratory, as illustrated in Fig. 3. The test rig comprises
six components including drive motor, load motor, mes-
sage encoder, shaft coupling, vibration acquisition equipment,
and electric control cabinet. The bearings under test are of
type 6204, featuring eight-ball configurations. Throughout the
experiments, an acceleration sensor was placed at load side
of the fault motor to acquire vibration data at a sampling
frequency of 12 kHz. During the signal acquisition phase,
the motor’s rotation speed and torque were manually adjusted
within ranges of 0–3000 r/min and 0–1.5 Nm, respectively.
Six kinds of bearing faults and one normal condition were
simulated on the platform as shown in Table I.
During this experiment, the rotating speed is kept at
1200 rpm and load torque is 1 Nm; all data collection lasted
20 seconds, resulting in a total of 240 000 sampling points.
Each sample contains 1024 points in time domain and is nor-
malized using min–max normalization. Three fault scenarios,
including different sampling methods, unbalanced ratios, and
generation models, are simulated by manually partitioning the
data into different dataset. To minimize the randomness in
neural network training, a fivefold cross-validation process is
utilized. In this process, the model iteration with last epoch in
each fold is selected to assess the accuracy of dataset.
B. Experiment 1: Different Sample Methods
Data are sampled using different steps: 128, 256, 512,
and 1024. In data splitting phase, samples from steps 128,
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 6 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
Fig. 4. Visualization of one of the five experimental improved CNN results.
(a) Confusion matrix. (b) t-SNE Visualization of Features.
256, and 512 overlapped to varying degrees, contrasting with
nonoverlapping samples from step 1024. Reflecting the overall
distribution of sampling points, 200 samples from overlapping
categories are allocated to training data, and 100 samples
from nonoverlapping category are also designated for training
purposes. For a realistic simulation of industrial application,
100 nonoverlapping samples are specifically set aside for test
data set.
In this study, a traditional CNN algorithm and four cutting-
edge CNN-based algorithms for comparison with the proposed
classifier model. All models are configured with the same
hyperparameters: learning rate of 1 ×10−4, batch size of 64,
and training duration of 50 epochs. The details and purposes
of these comparison methods are listed as follows.
1)CNN [28]: CNN serves as a benchmark for deep
learning-based intelligent diagnosis of rotating machin-
ery, providing valuable insights into its strengths and
areas for improvement.
2)MIXCNN [31]: MIXCNN enhances fault diagnosis by
combining depthwise and traditional convolutions, resid-
ual connections, and large kernels, achieving superior
accuracy and efficiency.
3)DRSN-CS [32]: DRSN-CS introduces deep residual
shrinkage networks with soft thresholding, effectively
removing noise and automatically improving fault
diagnosis accuracy.
4)MTAGN [33]: MTAGN is a multitask fault diagnosis
model designed for small sample scenarios, leveraging
shared features, task-specific attention, and adaptive
weighting for enhanced performance.
5)ResNet-HAM [34]: ResNet-HAM is a lightweight and
efficient attention mechanism for ResNet, integrating
channel attention and spatial attention to improve feature
representation.
To present the results more intuitively, one of the five
experimental outcomes is visualized in Fig. 4, the confusion
matrix is depicted in Fig. 4(a), and the t-SNE visualiza-
tion is shown in Fig. 4(b). The experimental outcomes are
presented in Table IIand the losses for each mode are
shown in Fig. 5. These results demonstrate that the improved
CNN achieves the highest average accuracy across fivefold
experiments, reaching 99.66%, thereby consistently exhibiting
superior diagnostic capabilities compared to the other methods.
It shows improvements of 2.74%, 2.17%, 2.00%, and 8.51%TABLE II
PERFORMANCE OF DIFFERENT CNN M ODELS : COMPARISON
OFACCURACY , PARAMETERS ,AND TRAINING TIME
Fig. 5. Average loss curve of the training process.
over the baseline CNN. Furthermore, the improved CNN
exhibits superior stability relative to the other algorithms,
as indicated by its smallest standard deviation among the
methods compared. In terms of training time and parameters,
the proposed model has the highest parameter count but is
not the most time-consuming, indicating a balanced trade-off
between performance and computational efficiency.
As the step size increases, all models show improvements
in accuracy to varying degrees. However, when the step size
reaches 1024, there is a noticeable tendence of decreasing
accuracy. This decline is primarily due to the smaller number
of samples obtained using the nonoverlapping method within
a fixed total sample length. Overlapping methods, which use
smaller step sizes, generate more training samples compared
to nonoverlapping method. Constructing samples of length
1024 with an overlapping step reduces the total sample length
whereas maintaining a high accuracy rate.
To further evaluate the robustness of the improved CNN
against noise, Gaussian noise with varying signal-to-noise
ratios (SNRs) was added to raw vibration. The specific
expression is
SNR(dB) =10 log10/parenleftbiggPsignal
Pnoise/parenrightbigg
(11)
where Psignal stands for the useful signal power, Pnoise stands
for the noise power, and dB is a logarithmic unit used to
express the ratio of the two. The performance of the improved
CNN was compared to baseline methods, with each method
being tested at SNR levels of −4,−2, 0, 2, 4, and 6 dB for the
vibration signals. The results are displayed in Fig. 6. As the
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 7 ---
ZHAO et al.: DATA AUGMENTATION FAULT DIAGNOSIS OF ROLLING MACHINERY USING CONDITION DDPM 3517712
TABLE III
PERFORMANCE METRICS OF DIFFERENT UNBALANCED RATIO IN PMSM
Fig. 6. Diagnostic accuracy of different methods using PMSM bearing dataset
under different SNR conditions.
noise level increases, the diagnostic accuracy of all models
declines. However, the improved CNN proposed in this article
demonstrates significant advantages in noise resistance. At a
simulated noise level of −4 dB, the accuracy of the proposed
classification model reaches 83.66%, which is significantly
higher than that of other methods. Additionally, the improved
CNN demonstrates superior performance under moderate and
low noise conditions. These findings indicate that the improved
CNN effectively addresses the issue of mechanical fault clas-
sification and is highly capable of handling diagnostic signals
in noisy environments.
C. Experiment 2: Different Unbalanced Ratios
To validate the data generation ability of the condition
DDPM and the fault diagnosis ability of improved CNN
in small sample and unbalanced dataset, three unbalanced
datasets (B, C, and D) with ratios of 1:2, 1:4, and 1:10 are
generated based on PMSM bearing dataset. The number of
sample for specific fault classes is presented in Table IV, and
Dataset A is employed as the training set for generative model,TABLE IV
DETAILS OF BEARING DATASET FROM PMSM P LATFORM
whereas Dataset E is utilized as the test set for classification
algorithm.
In this experiment, two experimental approaches are utilized
to address the imbalance in this bearing dataset. The first
approach involves selecting a number of normal samples that
is equal to the number of faulty samples to achieve balance;
The second approach supplements the dataset with synthetic
samples generated through condition DDPM. Each rebalanced
dataset serves as training set for the classifier model described
in experiment 1, utilizing consistent hyperparameters across
all models.
The results are shown in Table III. This indicate that with
the number of training samples decreases, the accuracy of
all methods also declines. Nevertheless, it is evident that
the proposed generation and classification model consistently
outperforms other approaches in all scenarios, achieving sig-
nificantly higher average accuracy. When class-imbalanced
ratio of original data is 1:2 and 1:4, improved CNN obtain
97.34% and 95.51% diagnostic accuracy, which is the highest
accuracy for all classification models. When unbalanced ratio
reached 1:10, all models exhibits a significant decrease in
accuracy due to lack of sufficient learnable features. Despite
this challenge, the proposed method maintains an accuracy
of 81.31%, demonstrating its ability to capture characteristic
features of minority classes. For small samples, the diagno-
sis accuracy exhibits variability compared to the unbalanced
dataset, primarily due to the interference of normal signals
with fault signal identification. Despite this, the improved
CNN consistently achieves the highest accuracy among the
models.
Furthermore, synthesizing data through condition DDPM
has significantly improved the performance of classifier model
across all imbalanced datasets. Notably, with this data aug-
mentation method, improved CNN maintains an accuracy level
exceeding 98%. This improvement is particularly pronounced
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 8 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
TABLE V
TRAINING HYPERPARAMETER SETTINGS OF GENERATIVE MODELS
TABLE VI
FAULT CLASSIFICATION OF DIFFERENT DATA
AUGMENTATION ON BEARING DATASET D
in the scenario with a ratio of 1:10, where the accuracy of
the proposed model escalated from an initial 81.31%–98.09%,
achieving an increase of 16.78%. The accuracy improvements
in other classifier models suggest that condition DDPM can be
applied to enhance the learning of characteristics in conditions
with imbalanced samples.
D. Experiment 3: Different Generation Models
To evaluate the quality of data generated by the condi-
tion DDPM, five generative models and two oversampling
methods are constructed for comparison. Additionally, GAN-
train/GAN-test is introduced to separately illustrate the
utilization potential of the synthetic data. The generative
models include CGAN, WGAN, WGAN-GP, ACGAN, and
DDPM, whereas the oversampling methods include ADASYN
and SMOTEENN. All these models are applied to rebalance
dataset D and tested on dataset E. The generative methods
are trained on dataset A, and the oversampling methods
are specifically based on dataset D. The specific training
hyperparameter settings are shown in Table V.
In this experiment, different generative methods are con-
structed to illustrate the signal generation capability of
condition DDPM. The results are shown in Table VI.
It shows that the diagnostic accuracy of proposed model has
increased with data augmentation. With some classification
models, traditional oversampling methods even outperform
deep learning methods like CGAN, WGAN-GP, and ACGAN.
The GAN-based generation models are easily affected byTABLE VII
TOTAL TRAINING TIME OF GENERATIVE MODELS
Fig. 7. Compare the fault diagnosis accuracy under different number of
PMSM generated samples.
noise, which can result in synthetic data with blurred features
that cannot be classified effectively. Additionally, DDPM is
not universally effective across all classifier models without
involving label information, as this can lead to confusion
between generated samples with different fault types. How-
ever, condition DDPM performs much better than other
methods, achieving an accuracy of above 90% in all classifier
model and reaching up to 98.17% in the proposed classifier
model, even with an unbalanced ratio of 1:10. It means that
the data generated by proposed model is of greater variety and
quality due to labeling guidance in data generation process.
The total training times of the generative models are shown
in Table VII. It can be observed that the training time of the
proposed conditional DDPM is 1:07:86, which outperforms the
original DDPM and is longer than GAN-based methods. With
the incorporation of gradient penalty computation, the training
process for WGAN-GP demands more time than that of other
GAN architectures. The main reason for the extended training
time of the original DDPM is that training on individual
fault samples results in prolonged overall training duration.
As a result, the conditional DDPM with label information
facilitates the model training procedure and achieves good
performance in terms of computational efficiency and the
quality of synthetic samples.
The performance of proposed condition DDPM under dif-
ferent number of generated sample is shown in Fig. 7. Using
GAN-train/GAN-test to indicate the similarity and diversity of
synthetic data, it is observed that as the number of synthetic
sample increases, diagnosis accuracy of improved CNN also
increases. The training and testing accuracies are relatively
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 9 ---
ZHAO et al.: DATA AUGMENTATION FAULT DIAGNOSIS OF ROLLING MACHINERY USING CONDITION DDPM 3517712
TABLE VIII
PERFORMANCE METRICS OF DIFFERENT UNBALANCED RATIO IN CWRU
close to each other, indicating that generated data are similar
to real signal and contains valid information that expresses
the characteristics of fault class. When the generated sample
count reaches 200, both GAN-train and GAN-test achieve a
relatively stable state. As the number of sample continues to
increase, the fluctuations in GAN-test expand slightly, whereas
GAN-train continues to rise. This indicates that the diversity of
generated sample improves and model trained on real samples
does not fully recognize the generated samples, thus achieving
a state of a mixture of real and generated samples. By incor-
porating synthetic data into training process, the classification
model can more effectively explore distinguishing features
between various faults, leading to improved fault classification.
E. Experiment 4: Performance Evaluation on CWRU Dataset
The Case Western Reserve University (CWRU) dataset is a
widely applied experimental bearing dataset [35]. The test rig
includes a two-horsepower motor, a torque sensor, a power
meter, and an electronic controller. Single-point defects are
induced in the bearings using electrical discharge machining,
with fault diameters of 7, 14, 21, and 28 mils. Data collection
occurs at a sampling frequency of 12 kHz under four load
conditions: 0, 1, 2, and 3 horsepower. The dataset encompasses
bearings in both normal and various faulty states, categorized
into inner race fault (IF), outer race fault (OF), and ball
fault (BF).
In this experiment, three fault types with extent of 7,
14, 21 mils, and normal condition, are used to evaluate the
generation performance of the proposed method. The selected
data length is 1024, with a sliding window length of 256. The
dataset setup is similar with experiment 2 ( B∗,C∗,and D∗)
with unbalanced ratio of 1:2, 1:4, and 1:10 as shown in
Table IV.
To further evaluate the robustness of the improved CNN
against noise on another dataset, the same noise levels used
in experiment 1 were applied to the CWRU dataset. The
results are shown in Fig. 8. As the noise level increases,
the performance of all other models decreases significantly,
whereas the proposed classifier model maintains the highest
accuracy and is the least affected by the noise. Considering
comparison across different datasets, the same deep learning
methods used in experiment 2 and experiment 3 are chosen
for training and compared. The experimental results are shown
in Tables VIII and IXand Fig. 9. The average accuracy
of condition DDPM on the rebalanced dataset significantly
Fig. 8. Diagnostic accuracy of different methods using CWRU dataset under
different SNR conditions.
TABLE IX
FAULT CLASSIFICATION OF DIFFERENT GENERATIVE
METHODS ON CWRU B EARING DATASET D*
increases due to the sufficient synthetic data, rising from
89.30% to 98.96%. This demonstrates that the generated data
are of high quality and diversity. Additionally, the proposed
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 10 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
Fig. 9. Compare the fault diagnosis accuracy under different number of
CWRU generated samples.
classifier model obtains the highest accuracy among other
classifier models in CWRU dataset, further illustrating its
universality and effectiveness.
Compared to other data augmentation methods, the condi-
tion DDPM achieves the best accuracy in diagnosis across
different classifier models, as shown in Table IX, demon-
strating its universality and applicability to various classifier
models. Additionally, data augmentation with GAN-based
models consistently outperforms traditional oversampling
methods in most classifier models. However, the performance
of individual GAN derivative models shows slight variations,
primarily due to the lack of hyperparameter optimization,
which can lead to blurred features often perceived as noisy
data. Besides, in contrast to standard DDPM, which oper-
ates without label information, condition DDPM demonstrates
significantly enhanced performance. This improvement is
attributed to incorporation of category-specific information
for different fault types during generation process. By lever-
aging this detailed category information, condition DDPM
achieves superior generation quality, particularly in capturing
and emphasizing class-distinct features.
As illustrated in Fig. 9, with an increase in the number
of generated sample, GAN-train accuracy trend smooths and
stabilizes around 98%, while GAN-test accuracy reaches above
99%. Although there is a slight discrepancy between training
and testing data, which indicates that synthetic data are more
similar to real samples but has less diversity, this high accuracy
achieved by proposed data generation and classifier model
also illustrates that generated samples are equivalent to real
samples and can be effectively used to address data imbalance
problems.
V. A BLATION STUDIES
A. Effectiveness of the Improvements
Ablation experiments were conducted on Datasets A as
shown in Table IV, to evaluate the effectiveness of differentTABLE X
RESULTS OF THE ABLATION EXPERIMENT
Fig. 10. Time and frequency domain analysis of PMSM Dataset.
modules in the improved CNN, which include more channels,
adaptive pooling, and deformable convolution. The results,
shown in Table X, indicate that Model 4, incorporating all
three modules, achieves the best diagnostic performance.
Model 1 lacks channel expansion, resulting in a 1.94%
decrease in performance. Model 2 omits adaptive pooling,
leading to a 0.65% reduction in accuracy and an increase in
parameter count. Model 3 excludes deformable convolution,
which slightly reduces accuracy by 0.23%, with a minimal
impact on the number of parameters. These findings suggest
that incorporating these modules effectively enhances the
model’s diagnostic capabilities, achieving an optimal balance
between accuracy and efficiency.
B. Visualization of Synthetic Samples
To directly visualize the quality of synthetic samples from
the PMSM and CWRU dataset, a comparison between the time
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 11 ---
ZHAO et al.: DATA AUGMENTATION FAULT DIAGNOSIS OF ROLLING MACHINERY USING CONDITION DDPM 3517712
Fig. 11. Time and frequency domain analysis of CWRU Dataset.
domain and frequency spectra (obtained using Fast Fourier
Transform) of both synthetic and real samples under failure
conditions is illustrated in Figs. 10 and 11. It can be observed
that the synthetic samples generated by conditional DDPM not
only produce visually plausible outputs but also effectively
retain the key characteristic frequencies of the real samples
in the frequency domain and accurately preserve the pulse
fluctuations associated with faults in the time domain. This
indicates that the generative model successfully captures the
underlying statistical properties of real data and facilitatesthe generation of fault-specific samples, making it especially
valuable in fault diagnosis applications where real fault data
are limited.
VI. C ONCLUSION
In this article, a novel data augmentation fault diagnosis
method based on condition DDPM and improved CNN is
proposed. 1-D time-series vibration is utilized to train diffu-
sion model, and this data generation involves sampling from
Gaussian noise and applying a series of reverse denoising
process with the guidance of label information. Subsequently,
an improved CNN with deformable convolution is employed
to extend the perceptual range and enhance classifier model’s
ability to learn fault characteristics. Finally, a comprehensive
methodology for evaluating generated data is introduced, pro-
viding a means to assess both similarity and diversity. This
methodology detailed here encompasses the complete process
of data augmentation and fault classification. This approach
has been experimentally validated using PMSM and CWRU
bearing dataset, comparing with five classifier models along-
side seven data augmentation techniques. Experimental results
indicate that the proposed model can generate high-quality
samples and attains high fault classification accuracy, even
dealing with class-imbalanced datasets. These findings suggest
the model’s potential for application across various mechanical
systems, as well as its adaptability to other types of time-series
signals, such as sound and electrical current signals.
Since the dataset used in this study is collected in a fixed
operational environment, its applicability is limited to similar
work conditions. Additionally, the U-Net architecture used in
this study is complex and requires substantial computational
resources. Therefore, future work includes applying transfer
learning to this model for different scenarios, generating
synthetic data under various operational conditions, and
exploring different architectures of U-Net to reduce compute
consumption.
REFERENCES
[1] Y . Xu et al., “Multiattention-based feature aggregation convolutional
networks with dual focal loss for fault diagnosis of rotating machinery
under data imbalance conditions,” IEEE Trans. Instrum. Meas., vol. 73,
pp. 1–11, 2024.
[2] Y . Zhang et al., “Digital twin-driven partial domain adaptation network
for intelligent fault diagnosis of rolling bearing,” Rel. Eng. Syst. Saf.,
vol. 234, Jun. 2023, Art. no. 109186.
[3] B. Zhao et al., “Signal-to-signal translation for fault diagnosis of
bearings and gears with few fault samples,” IEEE Trans. Instrum. Meas.,
vol. 70, pp. 1–10, 2021.
[4] R. Liu, B. Yang, E. Zio, and X. Chen, “Artificial intelligence for fault
diagnosis of rotating machinery: A review,” Mech. Syst. Signal Process.,
vol. 108, pp. 33–47, Aug. 2018.
[5] Y . Wei et al., “A novel data augmentation and composite multiscale
network for mechanical fault diagnosis,” IEEE Trans. Instrum. Meas.,
vol. 72, pp. 1–12, 2023.
[6] Y . Lei, B. Yang, X. Jiang, F. Jia, N. Li, and A. K. Nandi, “Applications
of machine learning to machine fault diagnosis: A review and roadmap,”
Mech. Syst. Signal Process., vol. 138, Apr. 2020, Art. no. 106587.
[7] J. Sun, C. Yan, and J. Wen, “Intelligent bearing fault diagnosis method
combining compressed data acquisition and deep learning,” IEEE Trans.
Instrum. Meas., vol. 67, no. 1, pp. 185–195, Jan. 2017.
[8] Z. Zhang, C. Zhang, and H. Li, “Highly imbalanced fault diagnosis
of rolling bearings based on variational mode Gaussian distortion and
deep residual shrinkage networks,” IEEE Trans. Instrum. Meas., vol. 72,
pp. 1–11, 2023.
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 
--- Page 12 ---
3517712 IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT, VOL. 74, 2025
[9] G. Niu, E. Liu, X. Wang, P. Ziehl, and B. Zhang, “Enhanced discriminate
feature learning deep residual CNN for multitask bearing fault diagnosis
with information fusion,” IEEE Trans. Ind. Informat., vol. 19, no. 1,
pp. 762–770, Jan. 2023.
[10] P. Chen, C. Xu, Z. Ma, and Y . Jin, “A mixed samples-driven methodol-
ogy based on denoising diffusion probabilistic model for identifying
damage in carbon fiber composite structures,” IEEE Trans. Instrum.
Meas., vol. 72, pp. 1–11, 2023.
[11] Z. Ren, T. Lin, K. Feng, Y . Zhu, Z. Liu, and K. Yan, “A systematic
review on imbalanced learning methods in intelligent fault diagnosis,”
IEEE Trans. Instrum. Meas., vol. 72, pp. 1–35, 2023.
[12] Z. Jiang, L. Zhao, Y . Lu, Y . Zhan, and Q. Mao, “A semi-supervised
resampling method for class-imbalanced learning,” Expert Syst. Appl.,
vol. 221, Jul. 2023, Art. no. 119733.
[13] K. Yan, C. Lu, X. Ma, Z. Ji, and J. Huang, “Intelligent fault diagnosis for
air handing units based on improved generative adversarial network and
deep reinforcement learning,” Expert Syst. Appl., vol. 240, Apr. 2024,
Art. no. 122545.
[14] M. Li, D. Zou, S. Luo, Q. Zhou, L. Cao, and H. Liu, “A new gen-
erative adversarial network based imbalanced fault diagnosis method,”
Measurement, vol. 194, May 2022, Art. no. 111045.
[15] H. He, Y . Bai, E. A. Garcia, and S. Li, “ADASYN: Adaptive synthetic
sampling approach for imbalanced learning,” in Proc. IEEE Int. Joint
Conf. Neural Netw., Jun. 2008, pp. 1322–1328.
[16] G. E. A. P. A. Batista, R. C. Prati, and M. C. Monard, “A study
of the behavior of several methods for balancing machine learning
training data,” ACM SIGKDD Explor. Newslett., vol. 6, no. 1, pp. 20–29,
Jun. 2004.
[17] Y . Shi, J. Li, H. Li, and B. Yang, “An imbalanced data augmentation
and assessment method for industrial process fault classification with
application in air compressors,” IEEE Trans. Instrum. Meas., vol. 72,
pp. 1–10, 2023.
[18] X. Zhao, J. Yao, W. Deng, M. Jia, and Z. Liu, “Normalized conditional
variational auto-encoder with adaptive focal loss for imbalanced fault
diagnosis of bearing-rotor system,” Mech. Syst. Signal Process., vol. 170,
May 2022, Art. no. 108826.
[19] Z. Fu, Z. Liu, S. Ping, W. Li, and J. Liu, “TRA-ACGAN: A motor
bearing fault diagnosis model based on an auxiliary classifier generative
adversarial network and transformer network,” ISA Trans., vol. 149,
pp. 381–393, Jun. 2024.
[20] J. Huo, C. Qi, C. Li, and N. Wang, “Data augmentation fault diagnosis
method based on residual mixed self-attention for rolling bearings
under imbalanced samples,” IEEE Trans. Instrum. Meas., vol. 72,
pp. 1–14, 2023.
[21] J. Dai, J. Wang, L. Yao, W. Huang, and Z. Zhu, “Categorical feature
GAN for imbalanced intelligent fault diagnosis of rotating machinery,”
IEEE Trans. Instrum. Meas., vol. 72, pp. 1–12, 2023.
[22] S. Sun, H. Ding, H. Huang, Z. Zhao, D. Wang, and W. Xu, “A novel
cross-domain data augmentation and bearing fault diagnosis method
based on an enhanced generative model,” IEEE Trans. Instrum. Meas.,
vol. 73, pp. 1–9, 2024.
[23] J. Sohl-Dickstein, E. Weiss, N. Maheswaranathan, and S. Ganguli, “Deep
unsupervised learning using nonequilibrium thermodynamics,” in Proc.
Int. Conf. Mach. Learn., 2015, pp. 2256–2265.
[24] X. Yang, T. Ye, X. Yuan, W. Zhu, X. Mei, and F. Zhou, “A novel data
augmentation method based on denoising diffusion probabilistic model
for fault diagnosis under imbalanced data,” IEEE Trans. Ind. Informat.,
vol. 20, no. 5, pp. 7820–7831, Feb. 2024.
[25] T. Zhang, J. Lin, J. Jiao, H. Zhang, and H. Li, “An interpretable latent
denoising diffusion probabilistic model for fault diagnosis under limited
data,” IEEE Trans. Ind. Informat., vol. 20, no. 8, pp. 10354–10365,
Aug. 2024.
[26] J. Ho, A. Jain, and P. Abbeel, “Denoising diffusion probabilistic models,”
inProc. Adv. Neural Inf. Process. Syst., vol. 33, 2020, pp. 6840–6851.
[27] J. Ho and T. Salimans, “Classifier-free diffusion guidance,” 2022,
arXiv:2207.12598.
[28] Z. Zhao et al., “Deep learning algorithms for rotating machinery
intelligent diagnosis: An open source benchmark study,” ISA Trans.,
vol. 107, pp. 224–255, Dec. 2020.[29] J. Dai et al., “Deformable convolutional networks,” in Proc. IEEE Int.
Conf. Comput. Vis. (ICCV), Oct. 2017, pp. 764–773.
[30] K. Shmelkov, C. Schmid, and K. Alahari, “How good is my GAN,” in
Proc. Eur. Conf. Comput. Vis. (ECCV), Jul. 2018, pp. 213–229.
[31] Z. Zhao and Y . Jiao, “A fault diagnosis method for rotating machinery
based on CNN with mixed information,” IEEE Trans. Ind. Informat.,
vol. 19, no. 8, pp. 9091–9101, Aug. 2023.
[32] M. Zhao, S. Zhong, X. Fu, B. Tang, and M. Pecht, “Deep residual
shrinkage networks for fault diagnosis,” IEEE Trans. Ind. Informat.,
vol. 16, no. 7, pp. 4681–4690, Jul. 2020.
[33] Z. Xie, J. Chen, Y . Feng, K. Zhang, and Z. Zhou, “End to end multi-task
learning with attention for multi-objective fault diagnosis under small
sample,” J. Manuf. Syst., vol. 62, pp. 301–316, Jan. 2022.
[34] G. Li, Q. Fang, L. Zha, X. Gao, and N. Zheng, “HAM: Hybrid attention
module in deep convolutional neural networks for image classification,”
Pattern Recognit., vol. 129, Sep. 2022, Art. no. 108785.
[35] W. A. Smith and R. B. Randall, “Rolling element bearing diagnostics
using the Case Western Reserve University data: A benchmark study,”
Mech. Syst. Signal Process., vols. 64–65, pp. 100–131, Dec. 2015.
Yao Zhao (Member, IEEE) received B.S. degree
in automation from Anhui University, Hefei, China,
in 2009, the M.S. degree in electrical engineering
from Shanghai Maritime University, Shanghai,
China, in 2011, and the Ph.D. degree from Nanjing
University of Aeronautics and Astronautics,
Nanjing, China, in 2016.
He is currently a Professor with Shanghai
University of Electric Power, Shanghai. His main
research interests include flexible interconnection
of distribution networks, situational awareness,
protection, planning, and power equipment fault diagnosis.
Tianming Sheng received the B.S. degree in electri-
cal engineering from Shanghai University of Electric
Power, Shanghai, China, in 2020, where he is
currently pursuing the M.S. degree in electrical
engineering.
His main research interests include intelligent fault
diagnosis and deep learning.
Dongdong Li (Member, IEEE) received the B.S.
degree in electrical engineering from Zhejiang
University, Hangzhou, China, in 1998, and the
Ph.D. degree from Shanghai Jiao Tong University,
Shanghai, China, in 2005.
He is currently a Professor and the Dean with
the College of Electric Engineering, Shanghai Uni-
versity of Electric Power, Shanghai. His research
interests include the analysis of electric power sys-
tems, new energy systems, power equipment fault
diagnosis, and the power electronization of power
systems.
Authorized licensed use limited to: Tsinghua University. Downloaded on May 26,2025 at 02:55:56 UTC from IEEE Xplore.  Restrictions apply. 