"""
Test script to verify all modules are working correctly
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os

from config import Config
from data_generator import BearingDataGenerator, create_balanced_dataset, visualize_signals
from ddpm_model import ConditionalDDPM
from improved_cnn import ImprovedCNN, BasicCNN, count_parameters

def test_data_generator():
    """Test the data generator"""
    print("Testing data generator...")
    
    config = Config()
    generator = BearingDataGenerator(config)
    
    # Test signal generation for each fault type
    for fault_type in config.fault_types:
        signal = generator.generate_signal(fault_type)
        print(f"  {fault_type}: Generated signal shape {signal.shape}")
        assert len(signal) == config.sample_length, f"Signal length mismatch for {fault_type}"
    
    # Test dataset creation
    signals, labels, scaler = create_balanced_dataset(config, samples_per_class=10)
    print(f"  Dataset shape: {signals.shape}, Labels shape: {labels.shape}")
    assert signals.shape[0] == len(config.fault_types) * 10, "Dataset size mismatch"
    
    print("✓ Data generator test passed!")

def test_ddpm_model():
    """Test the DDPM model"""
    print("Testing DDPM model...")
    
    config = Config()
    model = ConditionalDDPM(config)
    
    # Test model creation
    print(f"  Model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Test forward pass
    batch_size = 4
    x = torch.randn(batch_size, 1, config.sample_length)
    labels = torch.randint(0, config.num_classes, (batch_size,))
    
    loss = model(x, labels)
    print(f"  Forward pass loss: {loss.item():.4f}")
    assert loss.item() > 0, "Loss should be positive"
    
    # Test sampling
    with torch.no_grad():
        samples = model.sample(2, torch.tensor([0, 1]), config.sample_length)
        print(f"  Generated samples shape: {samples.shape}")
        assert samples.shape == (2, 1, config.sample_length), "Sample shape mismatch"
    
    print("✓ DDPM model test passed!")

def test_cnn_models():
    """Test the CNN models"""
    print("Testing CNN models...")
    
    config = Config()
    
    # Test improved CNN
    improved_cnn = ImprovedCNN(config)
    basic_cnn = BasicCNN(config)
    
    print(f"  Improved CNN parameters: {count_parameters(improved_cnn):,}")
    print(f"  Basic CNN parameters: {count_parameters(basic_cnn):,}")
    
    # Test forward pass
    batch_size = 4
    x = torch.randn(batch_size, config.sample_length)
    
    # Test improved CNN
    output_improved = improved_cnn(x)
    print(f"  Improved CNN output shape: {output_improved.shape}")
    assert output_improved.shape == (batch_size, config.num_classes), "Output shape mismatch"
    
    # Test basic CNN
    output_basic = basic_cnn(x)
    print(f"  Basic CNN output shape: {output_basic.shape}")
    assert output_basic.shape == (batch_size, config.num_classes), "Output shape mismatch"
    
    print("✓ CNN models test passed!")

def test_visualization():
    """Test visualization functions"""
    print("Testing visualization...")
    
    config = Config()
    
    # Create test directory
    test_dir = './test_figures'
    os.makedirs(test_dir, exist_ok=True)
    
    # Test signal visualization
    try:
        visualize_signals(config, os.path.join(test_dir, 'test_signals.png'))
        print("  ✓ Signal visualization test passed!")
    except Exception as e:
        print(f"  ✗ Signal visualization test failed: {e}")
    
    # Clean up
    if os.path.exists(test_dir):
        import shutil
        shutil.rmtree(test_dir)

def test_device_compatibility():
    """Test device compatibility"""
    print("Testing device compatibility...")
    
    config = Config()
    print(f"  Using device: {config.device}")
    
    # Test tensor operations on device
    x = torch.randn(10, 100).to(config.device)
    y = torch.randn(10, 100).to(config.device)
    z = x + y
    
    print(f"  Tensor operations on {config.device}: ✓")
    
    # Test model on device
    model = ImprovedCNN(config).to(config.device)
    input_tensor = torch.randn(2, 1024).to(config.device)
    output = model(input_tensor)
    
    print(f"  Model inference on {config.device}: ✓")
    print("✓ Device compatibility test passed!")

def run_quick_integration_test():
    """Run a quick integration test"""
    print("Running quick integration test...")
    
    config = Config()
    
    # Reduce parameters for quick test
    config.ddpm_epochs = 2
    config.cnn_epochs = 2
    
    # Create small dataset
    signals, labels, scaler = create_balanced_dataset(config, samples_per_class=5)
    print(f"  Created test dataset: {signals.shape}")
    
    # Test DDPM training (very brief)
    ddpm = ConditionalDDPM(config)
    
    # Convert to tensors
    signals_tensor = torch.FloatTensor(signals).unsqueeze(1)
    labels_tensor = torch.LongTensor(labels)
    
    # Quick training loop
    optimizer = torch.optim.Adam(ddpm.parameters(), lr=1e-3)
    
    for epoch in range(2):
        for i in range(0, len(signals_tensor), 8):
            batch_signals = signals_tensor[i:i+8]
            batch_labels = labels_tensor[i:i+8]
            
            loss = ddpm(batch_signals, batch_labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
    
    print(f"  DDPM training completed, final loss: {loss.item():.4f}")
    
    # Test sample generation
    with torch.no_grad():
        samples = ddpm.sample(2, torch.tensor([0, 1]), config.sample_length)
        print(f"  Generated samples: {samples.shape}")
    
    # Test CNN training
    from torch.utils.data import DataLoader, TensorDataset
    
    dataset = TensorDataset(torch.FloatTensor(signals), torch.LongTensor(labels))
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
    
    cnn = ImprovedCNN(config)
    criterion = torch.nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(cnn.parameters(), lr=1e-3)
    
    for epoch in range(2):
        for data, labels_batch in dataloader:
            outputs = cnn(data)
            loss = criterion(outputs, labels_batch)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
    
    print(f"  CNN training completed, final loss: {loss.item():.4f}")
    
    print("✓ Quick integration test passed!")

def main():
    """Run all tests"""
    print("="*60)
    print("RUNNING MODULE TESTS")
    print("="*60)
    
    try:
        test_device_compatibility()
        print()
        
        test_data_generator()
        print()
        
        test_ddpm_model()
        print()
        
        test_cnn_models()
        print()
        
        test_visualization()
        print()
        
        run_quick_integration_test()
        print()
        
        print("="*60)
        print("ALL TESTS PASSED! ✓")
        print("="*60)
        print("The system is ready to run the full pipeline.")
        print("You can now run: python main.py")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        print("\nPlease fix the issues before running the full pipeline.")

if __name__ == "__main__":
    main()
