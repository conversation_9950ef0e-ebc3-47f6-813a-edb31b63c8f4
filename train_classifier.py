"""
Training script for the improved CNN classifier
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import seaborn as sns
from tqdm import tqdm
import os

from config import Config
from improved_cnn import ImprovedCNN, BasicCNN
from data_generator import create_imbalanced_dataset, BearingDataset
from train_ddpm import generate_samples

def train_classifier(model, train_loader, val_loader, config=Config(), model_name="classifier"):
    """Train a classifier model"""

    device = config.device
    model = model.to(device)

    # Loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=config.cnn_learning_rate)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.5)

    # Training history
    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []

    print(f"Training {model_name} on {device}...")

    for epoch in range(config.cnn_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config.cnn_epochs} [Train]')
        for batch_idx, (data, labels) in enumerate(pbar):
            data, labels = data.to(device), labels.squeeze().to(device)

            optimizer.zero_grad()
            outputs = model(data)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()

            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })

        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for data, labels in val_loader:
                data, labels = data.to(device), labels.squeeze().to(device)
                outputs = model(data)
                loss = criterion(outputs, labels)

                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()

        # Calculate averages
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        train_acc = 100. * train_correct / train_total
        val_acc = 100. * val_correct / val_total

        # Store history
        train_losses.append(avg_train_loss)
        train_accuracies.append(train_acc)
        val_losses.append(avg_val_loss)
        val_accuracies.append(val_acc)

        print(f'Epoch {epoch+1}/{config.cnn_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.2f}%')

        scheduler.step()

    return {
        'train_losses': train_losses,
        'train_accuracies': train_accuracies,
        'val_losses': val_losses,
        'val_accuracies': val_accuracies
    }

def evaluate_classifier(model, test_loader, config=Config()):
    """Evaluate classifier performance"""

    device = config.device
    model.eval()

    all_predictions = []
    all_labels = []

    with torch.no_grad():
        for data, labels in test_loader:
            data, labels = data.to(device), labels.squeeze().to(device)
            outputs = model(data)
            _, predicted = torch.max(outputs, 1)

            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # Calculate accuracy
    accuracy = 100. * np.sum(np.array(all_predictions) == np.array(all_labels)) / len(all_labels)

    # Classification report
    report = classification_report(all_labels, all_predictions,
                                 target_names=config.fault_types,
                                 output_dict=True)

    # Confusion matrix
    cm = confusion_matrix(all_labels, all_predictions)

    return accuracy, report, cm, all_predictions, all_labels

def plot_training_history(history, save_path=None):
    """Plot training history"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # Loss plot
    ax1.plot(history['train_losses'], label='Train Loss')
    ax1.plot(history['val_losses'], label='Validation Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # Accuracy plot
    ax2.plot(history['train_accuracies'], label='Train Accuracy')
    ax2.plot(history['val_accuracies'], label='Validation Accuracy')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    plt.show()

def plot_confusion_matrix(cm, class_names, save_path=None):
    """Plot confusion matrix"""

    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    plt.show()

def experiment_with_augmentation(config=Config(), imbalance_ratio=4):
    """Experiment comparing with and without data augmentation"""

    print(f"Running experiment with imbalance ratio 1:{imbalance_ratio}")

    # Create imbalanced dataset
    print("Creating imbalanced dataset...")
    signals, labels, scaler = create_imbalanced_dataset(config, majority_samples=200, imbalance_ratio=imbalance_ratio)

    # Split into train and test
    X_train, X_test, y_train, y_test = train_test_split(
        signals, labels, test_size=0.2, random_state=42, stratify=labels
    )

    # Further split train into train and validation
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )

    print(f"Train set: {len(X_train)} samples")
    print(f"Validation set: {len(X_val)} samples")
    print(f"Test set: {len(X_test)} samples")

    # Create data loaders for original data
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.LongTensor(y_train))
    val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.LongTensor(y_val))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.LongTensor(y_test))

    train_loader = DataLoader(train_dataset, batch_size=config.cnn_batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=config.cnn_batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=config.cnn_batch_size, shuffle=False)

    # Train without augmentation
    print("\n" + "="*50)
    print("Training WITHOUT data augmentation")
    print("="*50)

    model_no_aug = ImprovedCNN(config)
    history_no_aug = train_classifier(model_no_aug, train_loader, val_loader, config, "ImprovedCNN (No Aug)")

    # Evaluate without augmentation
    acc_no_aug, report_no_aug, cm_no_aug, _, _ = evaluate_classifier(model_no_aug, test_loader, config)
    print(f"Accuracy without augmentation: {acc_no_aug:.2f}%")

    # Generate synthetic data using DDPM
    print("\n" + "="*50)
    print("Generating synthetic data using DDPM")
    print("="*50)

    # Check if DDPM model exists
    ddpm_model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')
    if not os.path.exists(ddpm_model_path):
        print("DDPM model not found. Please train DDPM first using train_ddpm.py")
        return

    # Generate synthetic samples for minority classes
    generated_signals, generated_labels, _ = generate_samples(ddpm_model_path, num_samples_per_class=50, config=config)

    # Filter only minority class samples (exclude normal class)
    minority_mask = generated_labels > 0
    minority_generated_signals = generated_signals[minority_mask]
    minority_generated_labels = generated_labels[minority_mask]

    # Combine original training data with synthetic data
    X_train_aug = np.concatenate([X_train, minority_generated_signals], axis=0)
    y_train_aug = np.concatenate([y_train, minority_generated_labels], axis=0)

    print(f"Original training set: {len(X_train)} samples")
    print(f"Augmented training set: {len(X_train_aug)} samples")
    print(f"Added {len(minority_generated_signals)} synthetic samples")

    # Create augmented data loader
    train_dataset_aug = TensorDataset(torch.FloatTensor(X_train_aug), torch.LongTensor(y_train_aug))
    train_loader_aug = DataLoader(train_dataset_aug, batch_size=config.cnn_batch_size, shuffle=True)

    # Train with augmentation
    print("\n" + "="*50)
    print("Training WITH data augmentation")
    print("="*50)

    model_with_aug = ImprovedCNN(config)
    history_with_aug = train_classifier(model_with_aug, train_loader_aug, val_loader, config, "ImprovedCNN (With Aug)")

    # Evaluate with augmentation
    acc_with_aug, report_with_aug, cm_with_aug, _, _ = evaluate_classifier(model_with_aug, test_loader, config)
    print(f"Accuracy with augmentation: {acc_with_aug:.2f}%")

    # Compare results
    print("\n" + "="*50)
    print("COMPARISON RESULTS")
    print("="*50)
    print(f"Accuracy without augmentation: {acc_no_aug:.2f}%")
    print(f"Accuracy with augmentation: {acc_with_aug:.2f}%")
    print(f"Improvement: {acc_with_aug - acc_no_aug:.2f}%")

    # Plot results
    plot_training_history(history_no_aug, f'training_history_no_aug_ratio_{imbalance_ratio}.png')
    plot_training_history(history_with_aug, f'training_history_with_aug_ratio_{imbalance_ratio}.png')

    plot_confusion_matrix(cm_no_aug, config.fault_types, f'confusion_matrix_no_aug_ratio_{imbalance_ratio}.png')
    plot_confusion_matrix(cm_with_aug, config.fault_types, f'confusion_matrix_with_aug_ratio_{imbalance_ratio}.png')

    return {
        'acc_no_aug': acc_no_aug,
        'acc_with_aug': acc_with_aug,
        'improvement': acc_with_aug - acc_no_aug,
        'history_no_aug': history_no_aug,
        'history_with_aug': history_with_aug
    }

if __name__ == "__main__":
    config = Config()

    # Create directories
    os.makedirs(config.models_dir, exist_ok=True)
    os.makedirs(config.figures_dir, exist_ok=True)

    # Run experiments with different imbalance ratios
    results = {}
    for ratio in config.imbalance_ratios:
        print(f"\n{'='*60}")
        print(f"EXPERIMENT WITH IMBALANCE RATIO 1:{ratio}")
        print(f"{'='*60}")

        results[ratio] = experiment_with_augmentation(config, ratio)

    # Summary of all experiments
    print("\n" + "="*60)
    print("SUMMARY OF ALL EXPERIMENTS")
    print("="*60)

    for ratio, result in results.items():
        print(f"Imbalance ratio 1:{ratio}:")
        print(f"  Without augmentation: {result['acc_no_aug']:.2f}%")
        print(f"  With augmentation: {result['acc_with_aug']:.2f}%")
        print(f"  Improvement: {result['improvement']:.2f}%")
        print()

    print("All experiments completed!")
