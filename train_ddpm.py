"""
Training script for Conditional DDPM
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import os

from config import Config
from ddpm_model import ConditionalDDPM
from data_generator import create_balanced_dataset, BearingDataset

def train_ddpm(config=Config(), save_path='ddpm_model.pth'):
    """Train the conditional DDPM model"""

    # Create dataset
    print("Creating training dataset...")
    signals, labels, scaler = create_balanced_dataset(config, samples_per_class=300)

    # Convert to PyTorch tensors
    signals_tensor = torch.FloatTensor(signals).unsqueeze(1)  # Add channel dimension
    labels_tensor = torch.LongTensor(labels)

    # Create dataset and dataloader
    dataset = TensorDataset(signals_tensor, labels_tensor)
    dataloader = DataLoader(dataset, batch_size=config.ddpm_batch_size, shuffle=True)

    # Initialize model
    device = config.device
    model = ConditionalDDPM(config).to(device)

    # Optimizer
    optimizer = optim.Adam(model.parameters(), lr=config.ddpm_learning_rate)

    # Training loop
    model.train()
    losses = []

    print(f"Training DDPM on {device}...")
    print(f"Dataset size: {len(signals)} samples")
    print(f"Batch size: {config.ddpm_batch_size}")
    print(f"Number of epochs: {config.ddpm_epochs}")

    for epoch in range(config.ddpm_epochs):
        epoch_losses = []

        pbar = tqdm(dataloader, desc=f'Epoch {epoch+1}/{config.ddpm_epochs}')
        for batch_idx, (data, labels_batch) in enumerate(pbar):
            data = data.to(device)
            labels_batch = labels_batch.to(device)

            # Forward pass
            loss = model(data, labels_batch)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            epoch_losses.append(loss.item())
            pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

        avg_loss = np.mean(epoch_losses)
        losses.append(avg_loss)

        print(f'Epoch {epoch+1}/{config.ddpm_epochs}, Average Loss: {avg_loss:.4f}')

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            checkpoint_path = f'ddpm_checkpoint_epoch_{epoch+1}.pth'
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': avg_loss,
                'scaler': scaler
            }, checkpoint_path)
            print(f'Checkpoint saved: {checkpoint_path}')

    # Save final model
    torch.save({
        'model_state_dict': model.state_dict(),
        'config': config,
        'scaler': scaler,
        'losses': losses
    }, save_path)

    print(f'Training completed! Model saved to {save_path}')

    # Plot training loss
    plt.figure(figsize=(10, 6))
    plt.plot(losses)
    plt.title('DDPM Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    plt.savefig('ddpm_training_loss.png', dpi=300, bbox_inches='tight')
    plt.show()

    return model, losses

def generate_samples(model_path, num_samples_per_class=50, config=Config()):
    """Generate synthetic samples using trained DDPM"""

    # Load trained model
    checkpoint = torch.load(model_path, map_location=config.device, weights_only=False)

    model = ConditionalDDPM(config).to(config.device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    scaler = checkpoint['scaler']

    print(f"Generating {num_samples_per_class} samples per class...")

    all_generated_signals = []
    all_generated_labels = []

    with torch.no_grad():
        for class_idx in range(config.num_classes):
            print(f"Generating samples for class {config.fault_types[class_idx]}...")

            # Create labels tensor
            labels = torch.full((num_samples_per_class,), class_idx, dtype=torch.long, device=config.device)

            # Generate samples
            generated = model.sample(num_samples_per_class, labels, config.sample_length)

            # Convert to numpy and remove channel dimension
            generated_np = generated.squeeze(1).cpu().numpy()

            all_generated_signals.append(generated_np)
            all_generated_labels.extend([class_idx] * num_samples_per_class)

    # Concatenate all generated samples
    all_generated_signals = np.concatenate(all_generated_signals, axis=0)
    all_generated_labels = np.array(all_generated_labels)

    print(f"Generated {len(all_generated_signals)} synthetic samples")

    return all_generated_signals, all_generated_labels, scaler

def visualize_generated_samples(generated_signals, generated_labels, config=Config(), save_path=None):
    """Visualize generated samples"""

    fig, axes = plt.subplots(config.num_classes, 2, figsize=(15, 3*config.num_classes))

    for class_idx in range(config.num_classes):
        # Get samples for this class
        class_mask = generated_labels == class_idx
        class_samples = generated_signals[class_mask]

        if len(class_samples) > 0:
            # Take first sample
            sample = class_samples[0]
            t = np.linspace(0, len(sample)/config.sampling_rate, len(sample))

            # Time domain
            axes[class_idx, 0].plot(t, sample)
            axes[class_idx, 0].set_title(f'{config.fault_types[class_idx]} - Generated (Time Domain)')
            axes[class_idx, 0].set_xlabel('Time (s)')
            axes[class_idx, 0].set_ylabel('Amplitude')
            axes[class_idx, 0].grid(True)

            # Frequency domain
            freqs = np.fft.fftfreq(len(sample), 1/config.sampling_rate)
            fft_sample = np.abs(np.fft.fft(sample))

            pos_freqs = freqs[:len(freqs)//2]
            pos_fft = fft_sample[:len(fft_sample)//2]

            axes[class_idx, 1].plot(pos_freqs, pos_fft)
            axes[class_idx, 1].set_title(f'{config.fault_types[class_idx]} - Generated (Frequency Domain)')
            axes[class_idx, 1].set_xlabel('Frequency (Hz)')
            axes[class_idx, 1].set_ylabel('Magnitude')
            axes[class_idx, 1].set_xlim(0, 1000)
            axes[class_idx, 1].grid(True)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=config.dpi, bbox_inches='tight')

    plt.show()

if __name__ == "__main__":
    config = Config()

    # Create directories
    os.makedirs(config.models_dir, exist_ok=True)
    os.makedirs(config.figures_dir, exist_ok=True)

    # Train DDPM
    model_path = os.path.join(config.models_dir, 'conditional_ddpm.pth')
    model, losses = train_ddpm(config, model_path)

    # Generate samples
    print("\nGenerating synthetic samples...")
    generated_signals, generated_labels, scaler = generate_samples(model_path, num_samples_per_class=20, config=config)

    # Visualize generated samples
    print("\nVisualizing generated samples...")
    viz_path = os.path.join(config.figures_dir, 'generated_samples.png')
    visualize_generated_samples(generated_signals, generated_labels, config, viz_path)

    print("Training and generation completed!")
